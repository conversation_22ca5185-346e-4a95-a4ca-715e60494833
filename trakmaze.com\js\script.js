// Image Compressor JavaScript
class ImageCompressor {
    constructor() {
        this.images = [];
        this.compressedImages = [];
        try {
            this.initializeEventListeners();
            this.initializeNavigation();
        } catch (error) {
            console.error('Error initializing event listeners:', error);
        }
    }

    // Initialize navigation functionality
    initializeNavigation() {
        const navToggle = document.getElementById('navToggle');
        const navMenu = document.getElementById('navMenu');

        if (navToggle && navMenu) {
            navToggle.addEventListener('click', () => {
                navToggle.classList.toggle('active');
                navMenu.classList.toggle('active');
            });

            // Close menu when clicking on a link
            const navLinks = navMenu.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', () => {
                    navToggle.classList.remove('active');
                    navMenu.classList.remove('active');
                });
            });

            // Close menu when clicking outside
            document.addEventListener('click', (e) => {
                if (!navToggle.contains(e.target) && !navMenu.contains(e.target)) {
                    navToggle.classList.remove('active');
                    navMenu.classList.remove('active');
                }
            });
        }
    }

    // Initialize all event listeners
    initializeEventListeners() {
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const qualitySlider = document.getElementById('qualitySlider');
        const qualityValue = document.getElementById('qualityValue');
        const compressAllBtn = document.getElementById('compressAllBtn');
        const downloadAllBtn = document.getElementById('downloadAllBtn');
        const clearAllBtn = document.getElementById('clearAllBtn');

        // Check if all required elements exist (only for compressor pages)
        if (!uploadArea || !fileInput || !qualitySlider || !qualityValue ||
            !compressAllBtn || !downloadAllBtn || !clearAllBtn) {
            console.log('Compressor elements not found - this is normal for non-compressor pages');
            return;
        }

        // File upload events
        uploadArea.addEventListener('click', () => fileInput.click());
        uploadArea.addEventListener('dragover', this.handleDragOver.bind(this));
        uploadArea.addEventListener('dragleave', this.handleDragLeave.bind(this));
        uploadArea.addEventListener('drop', this.handleDrop.bind(this));
        fileInput.addEventListener('change', this.handleFileSelect.bind(this));

        // Quality slider
        qualitySlider.addEventListener('input', (e) => {
            qualityValue.textContent = e.target.value + '%';
        });

        // Button events
        compressAllBtn.addEventListener('click', this.compressAllImages.bind(this));
        downloadAllBtn.addEventListener('click', this.downloadAllImages.bind(this));
        clearAllBtn.addEventListener('click', this.clearAllImages.bind(this));
    }

    // Handle drag over event
    handleDragOver(e) {
        e.preventDefault();
        e.currentTarget.classList.add('dragover');
    }

    // Handle drag leave event
    handleDragLeave(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');
    }

    // Handle drop event
    handleDrop(e) {
        e.preventDefault();
        e.currentTarget.classList.remove('dragover');
        const files = Array.from(e.dataTransfer.files);
        this.processFiles(files);
    }

    // Handle file selection
    handleFileSelect(e) {
        const files = Array.from(e.target.files);
        this.processFiles(files);

        // Clear the file input value to allow re-uploading the same file
        e.target.value = '';
    }

    // Process selected files
    processFiles(files) {
        const imageFiles = files.filter(file => file.type.startsWith('image/'));
        
        if (imageFiles.length === 0) {
            alert('Please select valid image files.');
            return;
        }

        // Check file size limit (10MB)
        const oversizedFiles = imageFiles.filter(file => file.size > 10 * 1024 * 1024);
        if (oversizedFiles.length > 0) {
            alert(`Some files are too large (max 10MB): ${oversizedFiles.map(f => f.name).join(', ')}`);
            return;
        }

        imageFiles.forEach(file => this.addImageToPreview(file));
        this.showPreviewSection();
    }

    // Add image to preview
    async addImageToPreview(file) {
        const imageData = {
            file: file,
            name: file.name,
            originalSize: file.size,
            compressed: false
        };

        this.images.push(imageData);
        this.compressedImages.push(undefined); // Keep arrays in sync

        // Create preview element
        const previewElement = await this.createImagePreviewElement(imageData);
        document.getElementById('imagePreviewContainer').appendChild(previewElement);

        // Show preview section (in case it was hidden)
        this.showPreviewSection();

        // Update compress button text
        this.updateCompressButtonText();
    }

    // Create image preview element
    async createImagePreviewElement(imageData) {
        const div = document.createElement('div');
        div.className = 'image-item';
        // Set the correct index (images.length - 1 because we already pushed the image)
        const imageIndex = this.images.length - 1;
        div.dataset.imageId = imageIndex;

        // Create image preview
        const img = document.createElement('img');
        img.className = 'image-preview';
        img.src = URL.createObjectURL(imageData.file);

        // Create info section
        const infoDiv = document.createElement('div');
        infoDiv.className = 'image-info';
        
        const fileName = document.createElement('h4');
        fileName.textContent = imageData.name;
        
        const originalSize = document.createElement('p');
        originalSize.textContent = `Original size: ${this.formatFileSize(imageData.originalSize)}`;
        
        const status = document.createElement('p');
        status.className = 'compression-status';
        status.textContent = 'Ready for compression';

        infoDiv.appendChild(fileName);
        infoDiv.appendChild(originalSize);
        infoDiv.appendChild(status);

        // Create actions section
        const actionsDiv = document.createElement('div');
        actionsDiv.className = 'image-actions';
        
        const removeBtn = document.createElement('button');
        removeBtn.className = 'btn btn-danger btn-small';
        removeBtn.textContent = 'Remove';
        removeBtn.addEventListener('click', () => {
            // Get current index from DOM element
            const currentIndex = parseInt(div.dataset.imageId);
            this.removeImage(currentIndex);
        });

        actionsDiv.appendChild(removeBtn);

        div.appendChild(img);
        div.appendChild(infoDiv);
        div.appendChild(actionsDiv);

        return div;
    }

    // Show preview section
    showPreviewSection() {
        const previewSection = document.getElementById('imagePreviewSection');
        if (previewSection) {
            previewSection.style.display = 'block';
            // Scroll to preview section with smooth animation
            setTimeout(() => {
                previewSection.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 100);
        }
    }

    // Hide preview section
    hidePreviewSection() {
        document.getElementById('imagePreviewSection').style.display = 'none';
    }

    // Remove image
    removeImage(imageId) {
        this.images.splice(imageId, 1);
        this.compressedImages.splice(imageId, 1);
        
        // Remove from DOM
        const element = document.querySelector(`[data-image-id="${imageId}"]`);
        if (element) {
            element.remove();
        }

        // Update image IDs
        this.updateImageIds();

        // Hide section if no images
        if (this.images.length === 0) {
            this.hidePreviewSection();
        }

        // Update compress button text
        this.updateCompressButtonText();
    }

    // Update image IDs after removal
    updateImageIds() {
        const imageElements = document.querySelectorAll('.image-item');
        imageElements.forEach((element, index) => {
            element.dataset.imageId = index;
        });
    }

    // Compress all images
    async compressAllImages() {
        const compressBtn = document.getElementById('compressAllBtn');
        const downloadBtn = document.getElementById('downloadAllBtn');

        if (!compressBtn || !downloadBtn) {
            console.error('Required buttons not found');
            return;
        }

        // Check if there are images to compress
        if (this.images.length === 0) {
            alert('Please select images first.');
            return;
        }

        // Check if already compressed
        const hasCompressedImages = this.compressedImages.some(img => img !== undefined);
        if (hasCompressedImages) {
            alert('Images have already been compressed. Please click "Clear All" to start over.');
            return;
        }

        compressBtn.disabled = true;
        compressBtn.innerHTML = '<span class="loading"></span> Compressing...';
        downloadBtn.style.display = 'none';

        try {
            for (let i = 0; i < this.images.length; i++) {
                // Update progress
                compressBtn.innerHTML = `<span class="loading"></span> Compressing... (${i + 1}/${this.images.length})`;
                await this.compressImage(i);
            }

            // Compression completed successfully
            downloadBtn.style.display = 'inline-block';
            compressBtn.textContent = 'Compression Complete';
            compressBtn.style.backgroundColor = '#28a745'; // Green to indicate completion
            compressBtn.disabled = true; // Disable button after completion
            compressBtn.style.cursor = 'not-allowed';

            // Show reset instruction
            this.showResetInstruction();

        } catch (error) {
            console.error('Compression error:', error);
            alert('An error occurred during compression. Please try again.');
            compressBtn.textContent = 'Compress All Images';
            compressBtn.style.backgroundColor = ''; // Reset color
            compressBtn.disabled = false;
        }
    }



    // Show reset instruction after compression
    showResetInstruction() {
        const instructionDiv = document.getElementById('resetInstruction');
        if (instructionDiv) {
            instructionDiv.style.display = 'block';
        }
    }

    // Hide reset instruction
    hideResetInstruction() {
        const instructionDiv = document.getElementById('resetInstruction');
        if (instructionDiv) {
            instructionDiv.style.display = 'none';
        }
    }

    // Update compress button text based on current state
    updateCompressButtonText() {
        const compressBtn = document.getElementById('compressAllBtn');
        if (!compressBtn) return;

        // Always reset to initial state when updating button text
        compressBtn.textContent = 'Compress All Images';
        compressBtn.style.backgroundColor = ''; // Default
        compressBtn.disabled = false;
        compressBtn.style.cursor = '';

        // Hide reset instruction
        this.hideResetInstruction();
    }

    // Compress single image
    async compressImage(imageIndex) {
        const imageData = this.images[imageIndex];

        const qualitySlider = document.getElementById('qualitySlider');
        const formatSelect = document.getElementById('formatSelect');

        if (!qualitySlider || !formatSelect) {
            throw new Error('Required form elements not found');
        }

        const quality = qualitySlider.value / 100;
        const format = formatSelect.value;

        return new Promise((resolve, reject) => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();

            img.onload = () => {
                // Get resize option
                const resizeSelect = document.getElementById('resizeSelect');
                const resizeOption = resizeSelect ? resizeSelect.value : 'none';
                let newWidth = img.width;
                let newHeight = img.height;

                // Apply resize if selected
                if (resizeOption !== 'none') {
                    const scale = parseInt(resizeOption) / 100;
                    newWidth = Math.round(img.width * scale);
                    newHeight = Math.round(img.height * scale);
                }

                // Set canvas dimensions
                canvas.width = newWidth;
                canvas.height = newHeight;

                // Draw image on canvas with potential resizing
                ctx.drawImage(img, 0, 0, newWidth, newHeight);

                // Determine optimal output format and quality
                const originalFormat = imageData.file.type;
                let outputFormat = originalFormat;
                let compressionQuality = quality;

                // Smart format selection
                if (format !== 'original') {
                    outputFormat = `image/${format}`;
                } else {
                    // Auto-select best format for compression
                    if (originalFormat === 'image/png') {
                        // Check if PNG has transparency
                        const hasTransparency = this.checkImageTransparency(canvas, ctx);
                        if (!hasTransparency && quality < 0.9) {
                            // No transparency and low quality - use JPEG for better compression
                            outputFormat = 'image/jpeg';
                        } else if (hasTransparency) {
                            // Has transparency - try WebP first, fallback to PNG
                            outputFormat = 'image/webp';
                        }
                    } else if (originalFormat === 'image/jpeg' && quality > 0.9) {
                        // High quality JPEG - try WebP for potentially better compression
                        outputFormat = 'image/webp';
                    }
                }

                // Adjust quality based on format
                if (outputFormat === 'image/png') {
                    // PNG doesn't use quality parameter, so we need to resize or use different approach
                    compressionQuality = undefined; // PNG ignores quality parameter
                } else if (outputFormat === 'image/webp') {
                    // WebP supports quality parameter
                    compressionQuality = quality;
                } else if (outputFormat === 'image/jpeg') {
                    // JPEG supports quality parameter
                    compressionQuality = quality;
                }

                // Try compression with current settings
                canvas.toBlob((blob) => {
                    if (blob) {
                        // Check if compression actually reduced file size
                        if (blob.size >= imageData.originalSize && format === 'original') {
                            // If compressed file is larger, try different approach
                            this.tryAlternativeCompression(canvas, imageData, imageIndex, resolve, reject);
                        } else {
                            this.compressedImages[imageIndex] = {
                                blob: blob,
                                name: this.getCompressedFileName(imageData.name, format),
                                originalSize: imageData.originalSize,
                                compressedSize: blob.size
                            };

                            // Update UI
                            this.updateImageStatus(imageIndex);
                            resolve();
                        }
                    } else {
                        reject(new Error('Failed to compress image'));
                    }
                }, outputFormat, compressionQuality);
            };

            img.onerror = () => reject(new Error('Failed to load image'));
            img.src = URL.createObjectURL(imageData.file);
        });
    }

    // Try alternative compression methods
    tryAlternativeCompression(canvas, imageData, imageIndex, resolve, reject) {
        const qualitySlider = document.getElementById('qualitySlider');
        const quality = qualitySlider ? qualitySlider.value / 100 : 0.8;
        const originalFormat = imageData.file.type;

        // Try JPEG compression for better file size reduction
        canvas.toBlob((jpegBlob) => {
            if (jpegBlob && jpegBlob.size < imageData.originalSize) {
                this.compressedImages[imageIndex] = {
                    blob: jpegBlob,
                    name: this.getCompressedFileName(imageData.name, 'jpeg'),
                    originalSize: imageData.originalSize,
                    compressedSize: jpegBlob.size
                };
                this.updateImageStatus(imageIndex);
                resolve();
            } else {
                // If still larger, try WebP
                canvas.toBlob((webpBlob) => {
                    if (webpBlob && webpBlob.size < imageData.originalSize) {
                        this.compressedImages[imageIndex] = {
                            blob: webpBlob,
                            name: this.getCompressedFileName(imageData.name, 'webp'),
                            originalSize: imageData.originalSize,
                            compressedSize: webpBlob.size
                        };
                        this.updateImageStatus(imageIndex);
                        resolve();
                    } else {
                        // If all attempts fail, return original file
                        this.compressedImages[imageIndex] = {
                            blob: imageData.file,
                            name: imageData.name,
                            originalSize: imageData.originalSize,
                            compressedSize: imageData.file.size
                        };
                        this.updateImageStatus(imageIndex);
                        resolve();
                    }
                }, 'image/webp', quality);
            }
        }, 'image/jpeg', quality);
    }

    // Check if image has transparency
    checkImageTransparency(canvas, ctx) {
        try {
            const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
            const data = imageData.data;

            // Check alpha channel (every 4th value)
            for (let i = 3; i < data.length; i += 4) {
                if (data[i] < 255) {
                    return true; // Found transparency
                }
            }
            return false; // No transparency found
        } catch (error) {
            // If we can't check (e.g., CORS issues), assume no transparency
            return false;
        }
    }

    // Get compressed file name
    getCompressedFileName(originalName, format) {
        if (format === 'original') return originalName;

        const nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.'));
        return `${nameWithoutExt}_compressed.${format}`;
    }

    // Update image status in UI
    updateImageStatus(imageIndex) {
        const element = document.querySelector(`[data-image-id="${imageIndex}"]`);
        if (!element) {
            console.error('Image element not found for index:', imageIndex);
            return;
        }

        const statusElement = element.querySelector('.compression-status');
        if (!statusElement) {
            console.error('Status element not found for image index:', imageIndex);
            return;
        }

        const compressedData = this.compressedImages[imageIndex];

        if (compressedData) {
            const compressionRatio = ((compressedData.originalSize - compressedData.compressedSize) / compressedData.originalSize * 100);

            // Handle different compression results
            if (compressionRatio > 0) {
                // Successful compression
                statusElement.className = 'compression-result';
                statusElement.textContent = `Compressed: ${this.formatFileSize(compressedData.compressedSize)} (${compressionRatio.toFixed(1)}% reduction)`;
            } else if (compressionRatio < 0) {
                // File became larger
                statusElement.className = 'compression-warning';
                statusElement.textContent = `Result: ${this.formatFileSize(compressedData.compressedSize)} (${Math.abs(compressionRatio).toFixed(1)}% larger - using original)`;
                statusElement.style.color = '#dc3545';
            } else {
                // Same size
                statusElement.className = 'compression-result';
                statusElement.textContent = `Result: ${this.formatFileSize(compressedData.compressedSize)} (no change)`;
                statusElement.style.color = '#6c757d';
            }

            // Add download button for individual image
            const actionsDiv = element.querySelector('.image-actions');
            if (actionsDiv && !actionsDiv.querySelector('.download-btn')) {
                const downloadBtn = document.createElement('button');
                downloadBtn.className = 'btn btn-secondary btn-small download-btn';
                downloadBtn.textContent = 'Download';
                downloadBtn.addEventListener('click', () => this.downloadSingleImage(imageIndex));
                actionsDiv.insertBefore(downloadBtn, actionsDiv.firstChild);
            }
        }
    }

    // Download single image
    downloadSingleImage(imageIndex) {
        const compressedData = this.compressedImages[imageIndex];
        if (!compressedData) return;

        const url = URL.createObjectURL(compressedData.blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = compressedData.name;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // Download all compressed images
    downloadAllImages() {
        this.compressedImages.forEach((compressedData, index) => {
            if (compressedData) {
                setTimeout(() => this.downloadSingleImage(index), index * 100);
            }
        });
    }

    // Clear all images
    clearAllImages() {
        this.images = [];
        this.compressedImages = [];
        document.getElementById('imagePreviewContainer').innerHTML = '';

        const downloadAllBtn = document.getElementById('downloadAllBtn');
        if (downloadAllBtn) {
            downloadAllBtn.style.display = 'none';
        }

        this.hidePreviewSection();

        // Reset file input
        const fileInput = document.getElementById('fileInput');
        if (fileInput) {
            fileInput.value = '';
        }

        // Reset compress button
        this.updateCompressButtonText();
    }

    // Format file size
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// Initialize the image compressor when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    try {
        console.log('Initializing Image Compressor...');
        new ImageCompressor();
        console.log('Image Compressor initialized successfully');
    } catch (error) {
        console.error('Failed to initialize Image Compressor:', error);
    }
});
