<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Terms of Use - trakmaze</title>
    <link rel="stylesheet" href="../css/styles.css">
</head>

<body>
    <!-- Navigation Header -->
    <nav class="top-navigation">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="../index.html" class="brand-link">
                    <svg class="brand-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <circle cx="8.5" cy="8.5" r="1.5"></circle>
                        <polyline points="21,15 16,10 5,21"></polyline>
                    </svg>
                    <span class="brand-text">trakmaze</span>
                </a>
            </div>

            <div class="nav-menu" id="navMenu">
                <ul class="nav-links">
                    <li><a href="../index.html" class="nav-link">Home</a></li>
                    <li><a href="compressor.html" class="nav-link">Image Compressor</a></li>
                    <li><a href="blog.html" class="nav-link">Blog</a></li>
                    <li><a href="about-us.html" class="nav-link">About Us</a></li>
                    <li><a href="contact-us.html" class="nav-link">Contact Us</a></li>
                    <li><a href="privacy-policy.html" class="nav-link">Privacy Policy</a></li>
                </ul>
            </div>

            <div class="nav-toggle" id="navToggle">
                <span class="nav-toggle-bar"></span>
                <span class="nav-toggle-bar"></span>
                <span class="nav-toggle-bar"></span>
            </div>
        </div>
    </nav>

    <div class="page-container">
        <header class="page-header">
            <h1>Terms of Use</h1>
            <p>Terms and conditions for using trakmaze</p>
        </header>

        <main class="page-content">
            <nav class="top-nav">
                <a href="../index.html" class="back-link">← Back to Home</a>
            </nav>
            <h1>Terms of Use</h1>
            <p><strong>Last updated:</strong> August 1, 2025</p>

            <h2>1. Acceptance of Terms</h2>
            <p>By accessing and using trakmaze ("the Service"), you accept and agree to be bound by the terms
                and provision of this agreement. If you do not agree to abide by the above, please do not use this
                service.</p>

            <h2>2. Description of Service</h2>
            <p>trakmaze is a free, web-based tool that allows users to compress and optimize images directly in
                their browser. The service processes images locally without uploading them to external servers.</p>

            <h2>3. Use License</h2>
            <p>Permission is granted to temporarily use trakmaze for personal and commercial purposes. This is
                the grant of a license, not a transfer of title, and under this license you may:</p>
            <ul>
                <li>Use the service for image compression and optimization</li>
                <li>Access the service from any compatible web browser</li>
                <li>Process unlimited images subject to technical limitations</li>
            </ul>

            <h2>4. Restrictions</h2>
            <p>You are specifically restricted from all of the following:</p>
            <ul>
                <li>Attempting to reverse engineer or extract the source code</li>
                <li>Using the service for any unlawful purpose or activity</li>
                <li>Attempting to compromise the security or integrity of the service</li>
                <li>Using automated systems to access the service excessively</li>
                <li>Redistributing or reselling access to the service</li>
            </ul>

            <h2>5. User Content and Privacy</h2>
            <p>trakmaze processes all images locally in your browser. We do not:</p>
            <ul>
                <li>Upload your images to our servers</li>
                <li>Store or retain any of your image data</li>
                <li>Access or view your images in any way</li>
                <li>Share your images with third parties</li>
            </ul>
            <p>You retain full ownership and rights to all images you process using our service.</p>

            <h2>6. Technical Limitations</h2>
            <p>The service is subject to certain technical limitations:</p>
            <ul>
                <li>Maximum file size of 10MB per image</li>
                <li>Processing speed depends on your device capabilities</li>
                <li>Requires a modern web browser with JavaScript enabled</li>
                <li>Internet connection required for initial page load only</li>
            </ul>

            <h2>7. Disclaimer</h2>
            <p>The information on this website is provided on an "as is" basis. To the fullest extent permitted by law,
                trakmaze:</p>
            <ul>
                <li>Excludes all representations and warranties relating to this website and its contents</li>
                <li>Does not guarantee the accuracy, completeness, or quality of compressed images</li>
                <li>Makes no warranties regarding the availability or functionality of the service</li>
            </ul>

            <h2>8. Limitation of Liability</h2>
            <p>trakmaze shall not be liable for any indirect, incidental, special, consequential, or punitive
                damages, including without limitation, loss of profits, data, use, goodwill, or other intangible losses,
                resulting from your use of the service.</p>

            <h2>9. Service Availability</h2>
            <p>We strive to maintain high availability of our service, but we do not guarantee:</p>
            <ul>
                <li>Uninterrupted access to the service</li>
                <li>Error-free operation</li>
                <li>Compatibility with all browsers or devices</li>
                <li>Permanent availability of the service</li>
            </ul>

            <h2>10. Modifications to Terms</h2>
            <p>trakmaze reserves the right to revise these terms of use at any time without notice. By using
                this service, you are agreeing to be bound by the then current version of these terms of use.</p>

            <h2>11. Governing Law</h2>
            <p>These terms and conditions are governed by and construed in accordance with applicable laws, and you
                irrevocably submit to the exclusive jurisdiction of the courts in that state or location.</p>

            <h2>12. Contact Information</h2>
            <p>If you have any questions about these Terms of Use, please contact us through our Contact Us page.</p>

            <p><strong>By using trakmaze, you acknowledge that you have read and understood these terms and
                    agree to be bound by them.</strong></p>

            <a href="../index.html" class="back-link">← Back to trakmaze</a>
        </main>

        <footer>
            <p>&copy; 2025 trakmaze.com All rights reserved. <br>All processing is done locally in your browser.</p>
            <div class="footer-links">
                <a href="about-us.html">About Us</a>
                <a href="contact-us.html">Contact Us</a>
                <a href="terms-of-use.html">Terms of Use</a>
                <a href="privacy-policy.html">Privacy Policy</a>
                <a href="terms-of-service.html">Terms of Service</a>
            </div>
        </footer>
    </div>

    <script>
        // Simple navigation toggle for mobile
        document.addEventListener('DOMContentLoaded', function () {
            const navToggle = document.getElementById('navToggle');
            const navMenu = document.getElementById('navMenu');

            if (navToggle && navMenu) {
                navToggle.addEventListener('click', () => {
                    navToggle.classList.toggle('active');
                    navMenu.classList.toggle('active');
                });

                // Close menu when clicking on a link
                const navLinks = navMenu.querySelectorAll('.nav-link');
                navLinks.forEach(link => {
                    link.addEventListener('click', () => {
                        navToggle.classList.remove('active');
                        navMenu.classList.remove('active');
                    });
                });

                // Close menu when clicking outside
                document.addEventListener('click', (e) => {
                    if (!navToggle.contains(e.target) && !navMenu.contains(e.target)) {
                        navToggle.classList.remove('active');
                        navMenu.classList.remove('active');
                    }
                });
            }
        });
    </script>
</body>

</html>