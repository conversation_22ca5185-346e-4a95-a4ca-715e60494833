// Blog Article Content Data
const articleContent = {
    'webp-complete-guide': {
        content: `
            <h2>Introduction to WebP</h2>
            <p>WebP is a modern image format developed by Google that provides superior lossless and lossy compression for images on the web. Using WebP, webmasters and web developers can create smaller, richer images that make the web faster.</p>
            
            <h2>Key Benefits of WebP</h2>
            <ul>
                <li><strong>Superior Compression:</strong> WebP lossless images are 26% smaller in size compared to PNGs</li>
                <li><strong>Quality Retention:</strong> WebP lossy images are 25-35% smaller than comparable JPEG images</li>
                <li><strong>Transparency Support:</strong> WebP supports lossless transparency with just 22% additional bytes</li>
                <li><strong>Animation Support:</strong> WebP supports animated images as an alternative to animated GIFs</li>
            </ul>
            
            <h2>Browser Support and Implementation</h2>
            <p>WebP is now supported by all major browsers including Chrome, Firefox, Safari, and Edge. However, it's important to implement proper fallbacks for older browsers.</p>
            
            <blockquote>
                "WebP typically achieves an average of 30% more compression than JPEG and JPEG 2000, without loss of image quality." - Google Developers
            </blockquote>
            
            <h2>Implementation Strategies</h2>
            <h3>Using the Picture Element</h3>
            <pre><code>&lt;picture&gt;
  &lt;source srcset="image.webp" type="image/webp"&gt;
  &lt;source srcset="image.jpg" type="image/jpeg"&gt;
  &lt;img src="image.jpg" alt="Description"&gt;
&lt;/picture&gt;</code></pre>
            
            <h3>Server-Side Detection</h3>
            <p>You can also implement WebP serving through server-side detection of the Accept header, automatically serving WebP to supporting browsers and fallback formats to others.</p>
            
            <h2>Conversion Tools and Techniques</h2>
            <p>There are several ways to convert your existing images to WebP format:</p>
            <ul>
                <li>Command-line tools like cwebp</li>
                <li>Online converters like TrakMaze</li>
                <li>Build tools and plugins for automated conversion</li>
                <li>CDN services with automatic format optimization</li>
            </ul>
            
            <h2>Best Practices</h2>
            <ol>
                <li>Always provide fallbacks for older browsers</li>
                <li>Test quality settings to find the optimal balance</li>
                <li>Consider your audience's browser usage statistics</li>
                <li>Implement progressive enhancement strategies</li>
                <li>Monitor performance improvements after implementation</li>
            </ol>
            
            <h2>Conclusion</h2>
            <p>WebP represents a significant advancement in image compression technology. By implementing WebP in your web projects, you can achieve substantial file size reductions while maintaining excellent image quality, leading to faster loading times and improved user experience.</p>
        `
    },
    'jpeg-compression-explained': {
        content: `
            <h2>Understanding JPEG Compression</h2>
            <p>JPEG (Joint Photographic Experts Group) compression is a lossy compression method specifically designed for photographic images. Understanding how it works helps you make better decisions about quality settings and optimization.</p>

            <h2>How JPEG Compression Works</h2>
            <p>JPEG compression works through several steps:</p>
            <ol>
                <li><strong>Color Space Conversion:</strong> RGB is converted to YCbCr color space</li>
                <li><strong>Chroma Subsampling:</strong> Color information is reduced while preserving luminance</li>
                <li><strong>Block Division:</strong> Image is divided into 8x8 pixel blocks</li>
                <li><strong>DCT Transform:</strong> Discrete Cosine Transform is applied to each block</li>
                <li><strong>Quantization:</strong> High-frequency components are reduced based on quality setting</li>
                <li><strong>Entropy Encoding:</strong> Final compression using Huffman coding</li>
            </ol>

            <h2>Quality Settings Explained</h2>
            <p>JPEG quality is typically expressed as a percentage from 1-100:</p>
            <ul>
                <li><strong>90-100%:</strong> Excellent quality, minimal compression artifacts</li>
                <li><strong>80-89%:</strong> High quality, good for professional photography</li>
                <li><strong>70-79%:</strong> Good quality, optimal for web use</li>
                <li><strong>60-69%:</strong> Acceptable quality, noticeable compression</li>
                <li><strong>Below 60%:</strong> Poor quality, significant artifacts</li>
            </ul>

            <h2>Finding the Perfect Balance</h2>
            <p>The key to effective JPEG compression is finding the sweet spot between file size and visual quality. This depends on several factors:</p>

            <h3>Image Content Considerations</h3>
            <ul>
                <li>High-detail images need higher quality settings</li>
                <li>Images with gradients are more sensitive to compression</li>
                <li>Images with sharp edges may show artifacts at low quality</li>
                <li>Skin tones require careful quality balance</li>
            </ul>

            <h3>Use Case Optimization</h3>
            <ul>
                <li><strong>Web thumbnails:</strong> 60-70% quality is often sufficient</li>
                <li><strong>Hero images:</strong> 80-85% quality for best impression</li>
                <li><strong>Product photos:</strong> 75-85% quality for detail preservation</li>
                <li><strong>Background images:</strong> 70-75% quality is typically adequate</li>
            </ul>

            <h2>Advanced JPEG Techniques</h2>
            <h3>Progressive JPEG</h3>
            <p>Progressive JPEG encoding allows images to load in multiple passes, improving perceived performance by showing a low-quality version first that gradually improves.</p>

            <h3>Chroma Subsampling</h3>
            <p>This technique reduces color information while preserving brightness, taking advantage of human visual perception to achieve better compression.</p>

            <h2>Common Mistakes to Avoid</h2>
            <ul>
                <li>Using JPEG for images with transparency</li>
                <li>Repeatedly saving JPEG files (generation loss)</li>
                <li>Using very low quality for important images</li>
                <li>Not considering the viewing context</li>
                <li>Ignoring file size requirements</li>
            </ul>

            <h2>Tools for JPEG Optimization</h2>
            <p>Several tools can help you optimize JPEG compression:</p>
            <ul>
                <li>TrakMaze online compressor with real-time preview</li>
                <li>Adobe Photoshop's "Save for Web" feature</li>
                <li>Command-line tools like jpegoptim</li>
                <li>Automated build tools and plugins</li>
            </ul>
        `
    },
    'web-performance-images': {
        content: `
            <h2>The Impact of Images on Web Performance</h2>
            <p>Images typically account for 60-70% of a website's total file size, making them the single largest factor affecting page load times. Understanding how to optimize images for web performance is crucial for creating fast, user-friendly websites.</p>

            <h2>Performance Metrics That Matter</h2>
            <h3>Core Web Vitals</h3>
            <ul>
                <li><strong>Largest Contentful Paint (LCP):</strong> Measures loading performance</li>
                <li><strong>First Input Delay (FID):</strong> Measures interactivity</li>
                <li><strong>Cumulative Layout Shift (CLS):</strong> Measures visual stability</li>
            </ul>

            <h3>Image-Specific Metrics</h3>
            <ul>
                <li><strong>Time to First Byte (TTFB):</strong> Server response time</li>
                <li><strong>First Contentful Paint (FCP):</strong> When first content appears</li>
                <li><strong>Speed Index:</strong> How quickly content is visually displayed</li>
            </ul>

            <h2>Optimization Strategies</h2>
            <h3>1. Choose the Right Format</h3>
            <ul>
                <li><strong>JPEG:</strong> Best for photographs and complex images</li>
                <li><strong>PNG:</strong> Best for images with transparency or simple graphics</li>
                <li><strong>WebP:</strong> Modern format with superior compression</li>
                <li><strong>AVIF:</strong> Next-generation format with even better compression</li>
            </ul>

            <h3>2. Implement Responsive Images</h3>
            <p>Use the srcset attribute and picture element to serve different image sizes based on device capabilities:</p>
            <pre><code>&lt;img src="image-800w.jpg"
     srcset="image-400w.jpg 400w,
             image-800w.jpg 800w,
             image-1200w.jpg 1200w"
     sizes="(max-width: 600px) 400px,
            (max-width: 1000px) 800px,
            1200px"
     alt="Description"&gt;</code></pre>

            <h3>3. Lazy Loading</h3>
            <p>Load images only when they're about to enter the viewport:</p>
            <pre><code>&lt;img src="image.jpg" loading="lazy" alt="Description"&gt;</code></pre>

            <h2>Advanced Techniques</h2>
            <h3>Image Preloading</h3>
            <p>Preload critical images that appear above the fold:</p>
            <pre><code>&lt;link rel="preload" as="image" href="hero-image.jpg"&gt;</code></pre>

            <h3>Progressive Enhancement</h3>
            <p>Start with a low-quality placeholder and progressively enhance:</p>
            <ol>
                <li>Show a blurred low-quality image placeholder</li>
                <li>Load the full-quality image in the background</li>
                <li>Replace the placeholder when loading is complete</li>
            </ol>

            <h2>SEO Benefits</h2>
            <p>Optimized images contribute to better SEO rankings through:</p>
            <ul>
                <li>Improved page load speeds</li>
                <li>Better user experience metrics</li>
                <li>Reduced bounce rates</li>
                <li>Higher engagement rates</li>
            </ul>

            <h2>Measuring Success</h2>
            <p>Use these tools to monitor your image optimization efforts:</p>
            <ul>
                <li>Google PageSpeed Insights</li>
                <li>GTmetrix</li>
                <li>WebPageTest</li>
                <li>Chrome DevTools</li>
            </ul>
        `
    },
    'png-optimization-techniques': {
        content: `
            <h2>Understanding PNG Compression</h2>
            <p>PNG (Portable Network Graphics) is a lossless compression format that's ideal for images with transparency, simple graphics, and images where quality preservation is paramount. Unlike JPEG, PNG compression doesn't discard any image data.</p>

            <h2>PNG Variants</h2>
            <h3>PNG-8</h3>
            <ul>
                <li>8-bit color depth (256 colors)</li>
                <li>Smaller file sizes</li>
                <li>Best for simple graphics and icons</li>
                <li>Supports 1-bit transparency</li>
            </ul>

            <h3>PNG-24</h3>
            <ul>
                <li>24-bit color depth (16.7 million colors)</li>
                <li>Larger file sizes</li>
                <li>Best for complex images</li>
                <li>No transparency support</li>
            </ul>

            <h3>PNG-32</h3>
            <ul>
                <li>24-bit color + 8-bit alpha channel</li>
                <li>Full transparency support</li>
                <li>Largest file sizes</li>
                <li>Best for images requiring transparency</li>
            </ul>

            <h2>Advanced Optimization Techniques</h2>
            <h3>1. Palette Optimization</h3>
            <p>For PNG-8 images, optimizing the color palette can significantly reduce file size:</p>
            <ul>
                <li>Reduce the number of colors in the palette</li>
                <li>Remove unused colors</li>
                <li>Use adaptive palettes based on image content</li>
                <li>Consider dithering for smooth gradients</li>
            </ul>

            <h3>2. Transparency Optimization</h3>
            <ul>
                <li>Remove unnecessary alpha channels</li>
                <li>Use PNG-8 with 1-bit transparency when possible</li>
                <li>Optimize alpha channel data</li>
                <li>Consider using CSS for simple transparency effects</li>
            </ul>

            <h3>3. Compression Level Tuning</h3>
            <p>PNG uses DEFLATE compression with different levels:</p>
            <ul>
                <li><strong>Level 0:</strong> No compression (fastest)</li>
                <li><strong>Level 1-3:</strong> Fast compression, larger files</li>
                <li><strong>Level 4-6:</strong> Balanced compression</li>
                <li><strong>Level 7-9:</strong> Maximum compression (slowest)</li>
            </ul>

            <h2>Tools and Techniques</h2>
            <h3>Command Line Tools</h3>
            <ul>
                <li><strong>OptiPNG:</strong> Lossless PNG optimizer</li>
                <li><strong>PNGOUT:</strong> Advanced PNG compressor</li>
                <li><strong>PNGCrush:</strong> PNG file size reducer</li>
                <li><strong>AdvPNG:</strong> Part of AdvanceCOMP utilities</li>
            </ul>

            <h3>Online Tools</h3>
            <ul>
                <li>TrakMaze PNG optimizer</li>
                <li>TinyPNG</li>
                <li>Kraken.io</li>
                <li>ImageOptim (Mac)</li>
            </ul>

            <h2>When to Use PNG vs Other Formats</h2>
            <h3>Use PNG When:</h3>
            <ul>
                <li>You need transparency</li>
                <li>Image quality is more important than file size</li>
                <li>Working with simple graphics or logos</li>
                <li>You need lossless compression</li>
            </ul>

            <h3>Consider Alternatives When:</h3>
            <ul>
                <li>Working with photographs (use JPEG)</li>
                <li>File size is critical (consider WebP)</li>
                <li>You don't need transparency (use JPEG)</li>
                <li>Working with animations (use GIF or WebP)</li>
            </ul>

            <h2>Best Practices</h2>
            <ol>
                <li>Always optimize PNG files before deployment</li>
                <li>Use PNG-8 when the color palette allows</li>
                <li>Remove metadata and unnecessary chunks</li>
                <li>Consider converting to WebP for modern browsers</li>
                <li>Use appropriate compression levels for your use case</li>
                <li>Test different optimization tools to find the best results</li>
            </ol>
        `
    },
    'responsive-images-guide': {
        content: `
            <h2>Introduction to Responsive Images</h2>
            <p>Responsive images adapt to different screen sizes, resolutions, and device capabilities, ensuring optimal user experience across all devices while minimizing bandwidth usage.</p>

            <h2>The srcset Attribute</h2>
            <p>The srcset attribute allows you to specify multiple image sources:</p>
            <pre><code>&lt;img src="image-800w.jpg"
     srcset="image-400w.jpg 400w,
             image-800w.jpg 800w,
             image-1200w.jpg 1200w"
     alt="Responsive image"&gt;</code></pre>

            <h2>The sizes Attribute</h2>
            <p>The sizes attribute tells the browser how much space the image will take:</p>
            <pre><code>&lt;img src="image.jpg"
     srcset="image-400w.jpg 400w, image-800w.jpg 800w"
     sizes="(max-width: 600px) 400px, 800px"
     alt="Responsive image"&gt;</code></pre>

            <h2>The Picture Element</h2>
            <p>For art direction and format switching:</p>
            <pre><code>&lt;picture&gt;
  &lt;source media="(min-width: 800px)" srcset="large.jpg"&gt;
  &lt;source media="(min-width: 400px)" srcset="medium.jpg"&gt;
  &lt;img src="small.jpg" alt="Responsive image"&gt;
&lt;/picture&gt;</code></pre>

            <h2>Best Practices</h2>
            <ul>
                <li>Always include a fallback src attribute</li>
                <li>Use appropriate breakpoints</li>
                <li>Consider bandwidth limitations</li>
                <li>Test on real devices</li>
            </ul>
        `
    },
    'image-formats-comparison': {
        content: `
            <h2>Modern Image Format Landscape</h2>
            <p>Choosing the right image format is crucial for web performance. Each format has its strengths and ideal use cases.</p>

            <h2>JPEG - The Veteran</h2>
            <h3>Strengths:</h3>
            <ul>
                <li>Universal browser support</li>
                <li>Excellent for photographs</li>
                <li>Small file sizes with good quality</li>
                <li>Progressive loading support</li>
            </ul>
            <h3>Weaknesses:</h3>
            <ul>
                <li>Lossy compression</li>
                <li>No transparency support</li>
                <li>Artifacts at low quality</li>
            </ul>

            <h2>PNG - The Quality Champion</h2>
            <h3>Strengths:</h3>
            <ul>
                <li>Lossless compression</li>
                <li>Transparency support</li>
                <li>Great for graphics and logos</li>
            </ul>
            <h3>Weaknesses:</h3>
            <ul>
                <li>Large file sizes for photos</li>
                <li>No animation support</li>
            </ul>

            <h2>WebP - The Modern Alternative</h2>
            <h3>Strengths:</h3>
            <ul>
                <li>25-35% smaller than JPEG</li>
                <li>26% smaller than PNG</li>
                <li>Supports transparency and animation</li>
                <li>Both lossy and lossless compression</li>
            </ul>
            <h3>Weaknesses:</h3>
            <ul>
                <li>Limited older browser support</li>
                <li>Requires fallbacks</li>
            </ul>

            <h2>AVIF - The Future</h2>
            <h3>Strengths:</h3>
            <ul>
                <li>50% smaller than JPEG</li>
                <li>Superior compression efficiency</li>
                <li>Wide color gamut support</li>
                <li>HDR support</li>
            </ul>
            <h3>Weaknesses:</h3>
            <ul>
                <li>Very limited browser support</li>
                <li>Slower encoding/decoding</li>
                <li>Requires careful implementation</li>
            </ul>

            <h2>Format Selection Guide</h2>
            <ul>
                <li><strong>Photographs:</strong> JPEG → WebP → AVIF</li>
                <li><strong>Graphics/Logos:</strong> PNG → WebP</li>
                <li><strong>Animations:</strong> GIF → WebP → AVIF</li>
                <li><strong>Transparency needed:</strong> PNG → WebP</li>
            </ul>
        `
    }
};

// Initialize article page
document.addEventListener('DOMContentLoaded', () => {
    loadArticle();
    setupShareButtons();
    setupRelatedArticles();
});

function loadArticle() {
    const urlParams = new URLSearchParams(window.location.search);
    const articleId = urlParams.get('id');
    
    if (!articleId) {
        showArticleNotFound();
        return;
    }
    
    // Find article in blog data
    const article = window.blogArticles ? 
        window.blogArticles.find(a => a.id === articleId) : 
        null;
    
    if (!article) {
        showArticleNotFound();
        return;
    }
    
    // Update page title and meta
    document.getElementById('articleTitle').textContent = article.title + ' - TrakMaze Blog';
    document.getElementById('breadcrumbTitle').textContent = article.title;
    document.getElementById('articleCategory').textContent = getCategoryName(article.category);
    document.getElementById('articleHeading').textContent = article.title;
    document.getElementById('articleAuthor').textContent = article.author;
    document.getElementById('articleDate').textContent = formatDate(article.date);
    document.getElementById('articleReadTime').textContent = article.readTime;
    document.getElementById('articleUpdated').textContent = formatDate(article.updated);

    // Load article content
    const articleBody = document.getElementById('articleBody');
    const content = articleContent[articleId];
    
    if (content) {
        articleBody.innerHTML = content.content;
    } else {
        // Generate placeholder content
        articleBody.innerHTML = generatePlaceholderContent(article);
    }
    
    // Display tags
    displayTags(article.tags);
}

function showArticleNotFound() {
    document.getElementById('articleHeading').textContent = 'Article Not Found';
    document.getElementById('articleBody').innerHTML = `
        <div style="text-align: center; padding: 60px 0;">
            <h2>Sorry, this article could not be found.</h2>
            <p>The article you're looking for might have been moved or doesn't exist.</p>
            <a href="blog.html" class="read-more-btn">Back to Blog</a>
        </div>
    `;
}

function generatePlaceholderContent(article) {
    return `
        <h2>Introduction</h2>
        <p>${article.excerpt}</p>
        
        <h2>Key Points</h2>
        <p>This article covers important aspects of ${article.title.toLowerCase()}. The content provides valuable insights for developers, designers, and anyone interested in image optimization and web performance.</p>
        
        <ul>
            <li>Comprehensive overview of the topic</li>
            <li>Practical implementation strategies</li>
            <li>Best practices and recommendations</li>
            <li>Real-world examples and use cases</li>
        </ul>
        
        <h2>Implementation Guide</h2>
        <p>Follow these steps to implement the techniques discussed in this article:</p>
        
        <ol>
            <li>Assess your current setup and requirements</li>
            <li>Choose the appropriate tools and methods</li>
            <li>Implement the optimization techniques</li>
            <li>Test and measure the results</li>
            <li>Iterate and improve based on feedback</li>
        </ol>
        
        <blockquote>
            "Proper image optimization is crucial for modern web performance and user experience."
        </blockquote>
        
        <h2>Best Practices</h2>
        <p>When implementing these techniques, keep these best practices in mind:</p>
        
        <ul>
            <li>Always test your optimizations thoroughly</li>
            <li>Consider your target audience and their devices</li>
            <li>Monitor performance metrics regularly</li>
            <li>Stay updated with the latest developments</li>
            <li>Balance quality with file size requirements</li>
        </ul>
        
        <h2>Conclusion</h2>
        <p>Understanding and implementing proper image optimization techniques is essential for creating fast, efficient websites. By following the guidelines and best practices outlined in this article, you can significantly improve your site's performance and user experience.</p>
        
        <p>For more advanced optimization techniques, try our <a href="compressor.html">TrakMaze Image Compressor</a> tool, which provides professional-grade compression with real-time preview and batch processing capabilities.</p>
    `;
}

function displayTags(tags) {
    const tagsContainer = document.getElementById('articleTags');
    if (!tags || tags.length === 0) {
        tagsContainer.style.display = 'none';
        return;
    }
    
    tagsContainer.innerHTML = '<h3>Tags:</h3>' + 
        tags.map(tag => `<span class="tag">${tag}</span>`).join('');
}

function setupShareButtons() {
    // Share functionality removed
}

function setupRelatedArticles() {
    const urlParams = new URLSearchParams(window.location.search);
    const currentArticleId = urlParams.get('id');
    
    if (!window.blogArticles) return;
    
    const currentArticle = window.blogArticles.find(a => a.id === currentArticleId);
    if (!currentArticle) return;
    
    // Find related articles (same category, excluding current)
    const relatedArticles = window.blogArticles
        .filter(a => a.category === currentArticle.category && a.id !== currentArticleId)
        .slice(0, 3);
    
    const relatedContainer = document.getElementById('relatedArticles');
    if (relatedArticles.length === 0) {
        relatedContainer.innerHTML = '<p>No related articles found.</p>';
        return;
    }
    
    relatedContainer.innerHTML = relatedArticles.map(article => `
        <div class="article-card">
            <div class="article-content">
                <div class="article-category">${getCategoryName(article.category)}</div>
                <h3><a href="blog-article.html?id=${article.id}">${article.title}</a></h3>
                <p class="article-excerpt">${article.excerpt}</p>
                <div class="article-meta">
                    <span class="author">By ${article.author}</span>
                    <span class="date">${formatDate(article.date)}</span>
                    <span class="read-time">${article.readTime}</span>
                </div>
                <a href="blog-article.html?id=${article.id}" class="read-more-btn">Read More</a>
            </div>
        </div>
    `).join('');
}

function getCategoryName(category) {
    const categoryNames = {
        'web-optimization': 'Web Optimization',
        'compression-techniques': 'Compression Techniques',
        'tutorials': 'Tutorials',
        'industry-insights': 'Industry Insights',
        'tools-tips': 'Tools & Tips'
    };
    return categoryNames[category] || category;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    });
}
