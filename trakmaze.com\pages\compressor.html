<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Compressor - TrakMaze</title>
    <link rel="stylesheet" href="../css/styles.css?v=0.0.2">
    <link rel="stylesheet" href="../css/compressor.css?v=0.0.1">
</head>

<body>
    <!-- Navigation Header -->
    <nav class="top-navigation">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="../index.html" class="brand-link">
                    <svg class="brand-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <circle cx="8.5" cy="8.5" r="1.5"></circle>
                        <polyline points="21,15 16,10 5,21"></polyline>
                    </svg>
                    <span class="brand-text">trakmaze</span>
                </a>
            </div>

            <div class="nav-menu" id="navMenu">
                <ul class="nav-links">
                    <li><a href="../index.html" class="nav-link">Home</a></li>
                    <li><a href="compressor.html" class="nav-link active">Image Compressor</a></li>
                    <li><a href="blog.html" class="nav-link">Blog</a></li>
                    <li><a href="about-us.html" class="nav-link">About Us</a></li>
                    <li><a href="contact-us.html" class="nav-link">Contact Us</a></li>
                    <li><a href="privacy-policy.html" class="nav-link">Privacy Policy</a></li>
                </ul>
            </div>

            <div class="nav-toggle" id="navToggle">
                <span class="nav-toggle-bar"></span>
                <span class="nav-toggle-bar"></span>
                <span class="nav-toggle-bar"></span>
            </div>
        </div>
    </nav>

    <div class="container">
        <header class="compressor-header">
            <h1>Professional Image Compressor</h1>
            <p>Compress your images with advanced settings and real-time preview</p>
            <div class="header-stats">
                <div class="stat-badge">
                    <span class="stat-icon">⚡</span>
                    <span>Instant Processing</span>
                </div>
                <div class="stat-badge">
                    <span class="stat-icon">🔒</span>
                    <span>100% Private</span>
                </div>
                <div class="stat-badge">
                    <span class="stat-icon">📊</span>
                    <span>Batch Support</span>
                </div>
            </div>
        </header>

        <main class="compressor-main">
            <!-- Quick Start Section -->
            <section class="quick-start-section">
                <h2>Quick Start</h2>
                <div class="quick-actions">
                    <button class="quick-btn" onclick="document.getElementById('fileInput').click()">
                        <span class="quick-icon">📁</span>
                        <span>Select Images</span>
                    </button>
                    <button class="quick-btn" onclick="compressor.compressAllImages()">
                        <span class="quick-icon">⚡</span>
                        <span>Auto Compress</span>
                    </button>
                    <button class="quick-btn" onclick="compressor.downloadAllImages()">
                        <span class="quick-icon">💾</span>
                        <span>Download All</span>
                    </button>
                    <button class="quick-btn" onclick="compressor.clearAllImages()">
                        <span class="quick-icon">🗑️</span>
                        <span>Clear All</span>
                    </button>
                </div>
            </section>

            <!-- Upload Section -->
            <section class="upload-section">
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <svg class="upload-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                            <polyline points="7,10 12,15 17,10"></polyline>
                            <line x1="12" y1="15" x2="12" y2="3"></line>
                        </svg>
                        <h3>Drop your images here</h3>
                        <p>or click to select files</p>
                        <div class="upload-features">
                            <span class="upload-feature">📸 JPG, PNG, WebP</span>
                            <span class="upload-feature">📏 Max 10MB each</span>
                            <span class="upload-feature">🔢 Multiple files</span>
                        </div>
                        <p class="compression-tip">💡 Tip: Use "Auto" format and 70-80% quality for best results</p>
                    </div>
                    <input type="file" id="fileInput" multiple accept="image/*" hidden>
                </div>
            </section>

            <!-- Advanced Settings Section -->
            <section class="settings-section">
                <h2>Compression Settings</h2>
                <div class="settings-grid">
                    <!-- Quality Settings -->
                    <div class="setting-card">
                        <h3>Quality Control</h3>
                        <div class="setting-group">
                            <label for="qualitySlider">Compression Quality:</label>
                            <div class="slider-container">
                                <input type="range" id="qualitySlider" min="10" max="100" value="80" step="5">
                                <span id="qualityValue">80%</span>
                            </div>
                            <div class="quality-presets">
                                <button class="preset-btn" onclick="setQuality(95)">High (95%)</button>
                                <button class="preset-btn" onclick="setQuality(80)">Medium (80%)</button>
                                <button class="preset-btn" onclick="setQuality(60)">Low (60%)</button>
                            </div>
                        </div>
                    </div>

                    <!-- Format Settings -->
                    <div class="setting-card">
                        <h3>Output Format</h3>
                        <div class="setting-group">
                            <label for="formatSelect">Choose Format:</label>
                            <select id="formatSelect">
                                <option value="original">🤖 Auto (Best Compression)</option>
                                <option value="jpeg">📷 JPEG (Smaller files)</option>
                                <option value="png">🖼️ PNG (Lossless)</option>
                                <option value="webp">🚀 WebP (Modern)</option>
                            </select>
                            <p class="format-info">Auto mode intelligently selects the best format for each image</p>
                        </div>
                    </div>

                    <!-- Resize Settings -->
                    <div class="setting-card">
                        <h3>Resize Options</h3>
                        <div class="setting-group">
                            <label for="resizeSelect">Resize Image:</label>
                            <select id="resizeSelect">
                                <option value="none">📐 Keep Original Size</option>
                                <option value="75">📏 75% of Original</option>
                                <option value="50">📐 50% of Original</option>
                                <option value="25">📏 25% of Original</option>
                                <option value="custom">⚙️ Custom Size</option>
                            </select>
                            <div id="customSizeInputs" style="display: none;">
                                <input type="number" id="customWidth" placeholder="Width (px)">
                                <input type="number" id="customHeight" placeholder="Height (px)">
                                <label><input type="checkbox" id="maintainAspect" checked> Maintain aspect ratio</label>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Options -->
                    <div class="setting-card">
                        <h3>Advanced Options</h3>
                        <div class="setting-group">
                            <label><input type="checkbox" id="removeMetadata" checked> Remove metadata (EXIF)</label>
                            <label><input type="checkbox" id="progressiveJPEG"> Progressive JPEG</label>
                            <label><input type="checkbox" id="optimizePNG"> Optimize PNG</label>
                            <label><input type="checkbox" id="autoRotate"> Auto-rotate based on EXIF</label>
                            <label><input type="checkbox" id="preserveTransparency" checked> Preserve
                                transparency</label>
                            <label><input type="checkbox" id="enablePreview" checked> Enable before/after
                                preview</label>
                        </div>
                    </div>

                    <!-- Batch Operations -->
                    <div class="setting-card">
                        <h3>🔄 Batch Operations</h3>
                        <div class="setting-group">
                            <label for="batchPrefix">File name prefix:</label>
                            <input type="text" id="batchPrefix" placeholder="e.g., compressed_">
                            <label for="batchSuffix">File name suffix:</label>
                            <input type="text" id="batchSuffix" placeholder="e.g., _optimized">
                            <button class="preset-btn" onclick="applyBatchRename()">Apply Rename</button>
                        </div>
                        <div class="setting-group">
                            <label>Quick Actions:</label>
                            <div class="batch-actions">
                                <button class="preset-btn" style="color:#fff" onclick="clearAllImages()"
                                    title="Remove all uploaded images">Clear All</button>
                                <button class="preset-btn" style="color:#fff" onclick="downloadAllCompressed()"
                                    title="Download all compressed images">Download All</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Compression Progress -->
            <section class="progress-section" id="progressSection" style="display: none;">
                <h2>Compression Progress</h2>
                <div class="progress-container">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">Ready to compress...</div>
                </div>
            </section>

            <!-- Image Preview Section -->
            <section class="image-preview-section" id="imagePreviewSection" style="display: none;">
                <div class="section-header">
                    <h2>Image Preview & Results</h2>
                    <div class="preview-controls">
                        <button class="control-btn" onclick="togglePreviewMode()">
                            <span id="previewModeIcon">🖼️</span>
                            <span id="previewModeText">Grid View</span>
                        </button>
                        <button class="control-btn" onclick="sortImages('name')">📝 Sort by Name</button>
                        <button class="control-btn" onclick="sortImages('size')">📊 Sort by Size</button>
                    </div>
                </div>
                <div id="imagePreviewContainer" class="preview-grid"></div>

                <!-- Batch Actions -->
                <div class="batch-actions">
                    <div class="action-group">
                        <button id="compressAllBtn" class="btn btn-primary">
                            <span class="btn-icon">⚡</span>
                            Compress All Images
                        </button>
                        <button id="downloadAllBtn" class="btn btn-secondary" style="display: none;">
                            <span class="btn-icon">💾</span>
                            Download All
                        </button>
                        <button id="clearAllBtn" class="btn btn-danger">
                            <span class="btn-icon">🗑️</span>
                            Clear All
                        </button>
                    </div>
                </div>

                <!-- Compression Summary -->
                <div class="compression-summary" id="compressionSummary" style="display: none;">
                    <h3>Compression Summary</h3>
                    <div class="summary-stats">
                        <div class="summary-stat">
                            <span class="stat-label">Total Images:</span>
                            <span class="stat-value" id="totalImages">0</span>
                        </div>
                        <div class="summary-stat">
                            <span class="stat-label">Original Size:</span>
                            <span class="stat-value" id="originalTotalSize">0 MB</span>
                        </div>
                        <div class="summary-stat">
                            <span class="stat-label">Compressed Size:</span>
                            <span class="stat-value" id="compressedTotalSize">0 MB</span>
                        </div>
                        <div class="summary-stat">
                            <span class="stat-label">Total Savings:</span>
                            <span class="stat-value" id="totalSavings">0%</span>
                        </div>
                    </div>
                </div>

                <div id="resetInstruction" class="reset-instruction" style="display: none;">
                    <p>✅ Compression completed! To compress again with different settings, please click "Clear All" and
                        re-upload your images.</p>
                </div>
            </section>

            <!-- Image Comparison Modal -->
            <div id="comparisonModal" class="modal" style="display: none;">
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>Before vs After Comparison</h3>
                        <button class="modal-close" onclick="closeComparisonModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <div class="comparison-container">
                            <div class="comparison-side">
                                <h4>Original</h4>
                                <img id="originalImage" src="" alt="Original">
                                <div class="image-details">
                                    <p>Size: <span id="originalSize"></span></p>
                                    <p>Dimensions: <span id="originalDimensions"></span></p>
                                </div>
                            </div>
                            <div class="comparison-divider">
                                <div class="vs-badge">VS</div>
                            </div>
                            <div class="comparison-side">
                                <h4>Compressed</h4>
                                <img id="compressedImage" src="" alt="Compressed">
                                <div class="image-details">
                                    <p>Size: <span id="compressedSize"></span></p>
                                    <p>Savings: <span id="compressionSavings"></span></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tutorial Section -->
            <section class="tutorial-section">
                <h2>Complete Usage Tutorial</h2>
                <div class="tutorial-content">
                    <div class="tutorial-step">
                        <div class="step-header">
                            <span class="step-number">1</span>
                            <h3>Getting Started - Upload Your Images</h3>
                        </div>
                        <div class="step-content">
                            <p><strong>Method 1: Drag & Drop</strong> - Simply drag your image files from your computer
                                and drop them onto the upload area. The system will automatically detect and process all
                                supported image formats (JPG, PNG, WebP).</p>
                            <p><strong>Method 2: Click to Browse</strong> - Click on the upload area or the "Select
                                Images" button to open your file browser. You can select multiple images at once by
                                holding Ctrl (Windows) or Cmd (Mac) while clicking.</p>
                            <p><strong>File Requirements:</strong> Each image must be under 10MB in size. Supported
                                formats include JPEG, PNG, and WebP. The system can handle hundreds of images
                                simultaneously for batch processing.</p>
                        </div>
                    </div>

                    <div class="tutorial-step">
                        <div class="step-header">
                            <span class="step-number">2</span>
                            <h3>Configure Compression Settings</h3>
                        </div>
                        <div class="step-content">
                            <p><strong>Quality Control:</strong> Use the quality slider to adjust compression level.
                                Higher values (85-95%) preserve more detail but result in larger files. Lower values
                                (50-70%) create smaller files with some quality loss. For web use, 70-80% is typically
                                optimal.</p>
                            <p><strong>Output Format Selection:</strong> Choose "Auto" for intelligent format selection,
                                or manually select JPEG (best for photos), PNG (best for graphics with transparency), or
                                WebP (modern format with superior compression).</p>
                            <p><strong>Resize Options:</strong> Reduce image dimensions to dramatically decrease file
                                size. Select from preset percentages or choose "Custom Size" for specific pixel
                                dimensions. Enable "Maintain aspect ratio" to prevent distortion.</p>
                            <p><strong>Advanced Options:</strong> Remove metadata to eliminate camera information and
                                reduce file size. Enable progressive JPEG for better web loading. Use PNG optimization
                                for lossless compression improvements.</p>
                        </div>
                    </div>

                    <div class="tutorial-step">
                        <div class="step-header">
                            <span class="step-number">3</span>
                            <h3>Preview and Adjust</h3>
                        </div>
                        <div class="step-content">
                            <p><strong>Real-time Preview:</strong> Once uploaded, images appear in the preview section
                                showing original file size and estimated compression results. Use the grid/list view
                                toggle to change display format.</p>
                            <p><strong>Individual Controls:</strong> Each image can be removed individually if needed.
                                Sort images by name or size using the control buttons for better organization.</p>
                            <p><strong>Quality Assessment:</strong> Before compressing, review the settings to ensure
                                they meet your requirements. Adjust quality or format settings if needed for optimal
                                results.</p>
                        </div>
                    </div>

                    <div class="tutorial-step">
                        <div class="step-header">
                            <span class="step-number">4</span>
                            <h3>Compress and Download</h3>
                        </div>
                        <div class="step-content">
                            <p><strong>Start Compression:</strong> Click "Compress All Images" to begin processing. The
                                progress bar shows real-time status, and each image displays its compression results
                                including file size reduction percentage.</p>
                            <p><strong>Review Results:</strong> After compression, each image shows before/after file
                                sizes and compression ratio. Green indicators show successful compression, while
                                warnings appear for images that couldn't be reduced.</p>
                            <p><strong>Download Options:</strong> Download individual images using the "Download" button
                                on each item, or use "Download All" to get all compressed images at once. Files are
                                automatically renamed with compression indicators.</p>
                            <p><strong>Batch Operations:</strong> Use the batch rename feature to add prefixes or
                                suffixes to all files. This is useful for organizing compressed images or maintaining
                                naming conventions.</p>
                        </div>
                    </div>

                    <div class="tutorial-step">
                        <div class="step-header">
                            <span class="step-number">5</span>
                            <h3>Advanced Features</h3>
                        </div>
                        <div class="step-content">
                            <p><strong>Comparison Tool:</strong> Click the "Compare" button on any compressed image to
                                see a side-by-side before/after comparison with detailed statistics including file sizes
                                and compression savings.</p>
                            <p><strong>Keyboard Shortcuts:</strong> Use Ctrl+U to upload files, Ctrl+Enter to compress
                                all images, Ctrl+D to download all, and Escape to clear all images for faster workflow.
                            </p>
                            <p><strong>Quality Presets:</strong> Use the High (95%), Medium (80%), and Low (60%) quality
                                presets for quick settings adjustment based on your specific use case.</p>
                            <p><strong>Custom Sizing:</strong> For precise control, use custom resize options with
                                specific pixel dimensions. The aspect ratio lock ensures proportional scaling to prevent
                                image distortion.</p>
                        </div>
                    </div>

                    <div class="tutorial-step">
                        <div class="step-header">
                            <span class="step-number">6</span>
                            <h3>Best Practices & Optimization</h3>
                        </div>
                        <div class="step-content">
                            <p><strong>Web Optimization:</strong> For website images, use 70-80% quality with WebP
                                format when possible. Enable progressive JPEG for better perceived loading speed.</p>
                            <p><strong>Print Materials:</strong> Maintain 85-95% quality for images intended for print.
                                Keep original dimensions unless file size is a concern.</p>
                            <p><strong>Social Media:</strong> Most platforms have size limits and will re-compress
                                images. Use 70-75% quality and resize to platform-specific dimensions for best results.
                            </p>
                            <p><strong>Storage Optimization:</strong> For archival purposes, use PNG optimization for
                                lossless compression or moderate JPEG compression (80-85%) to balance quality and
                                storage space.</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Tips Section -->
            <section class="tips-section">
                <h2>Pro Tips for Better Compression</h2>
                <div class="tips-grid">
                    <div class="tip-card">
                        <div class="tip-icon">🎯</div>
                        <h4>Choose the Right Quality</h4>
                        <p>For web use: 70-80%. For print: 85-95%. For thumbnails: 50-70%.</p>
                    </div>
                    <div class="tip-card">
                        <div class="tip-icon">📐</div>
                        <h4>Resize Before Compressing</h4>
                        <p>Reducing dimensions can dramatically decrease file size while maintaining visual quality.</p>
                    </div>
                    <div class="tip-card">
                        <div class="tip-icon">🔄</div>
                        <h4>Use Auto Format</h4>
                        <p>Let our algorithm choose the best format for each image to maximize compression.</p>
                    </div>
                    <div class="tip-card">
                        <div class="tip-icon">🗂️</div>
                        <h4>Remove Metadata</h4>
                        <p>EXIF data can add significant file size. Remove it unless you need location/camera info.</p>
                    </div>
                </div>
            </section>
        </main>

        <footer>
            <p>&copy; 2025 trakmaze.com All rights reserved. <br>All processing is done locally in your browser.</p>
            <div class="footer-links">
                <a href="about-us.html">About Us</a>
                <a href="contact-us.html">Contact Us</a>
                <a href="terms-of-use.html">Terms of Use</a>
                <a href="privacy-policy.html">Privacy Policy</a>
                <a href="terms-of-service.html">Terms of Service</a>
            </div>
        </footer>
    </div>

    <script src="../js/script.js"></script>
    <script src="../js/compressor-advanced.js"></script>
</body>

</html>