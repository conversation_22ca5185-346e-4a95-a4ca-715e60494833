// Advanced Compressor Features
let compressor;
let previewMode = 'grid'; // 'grid' or 'list'

// Initialize advanced features when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Wait for the main compressor to be initialized
    setTimeout(() => {
        if (window.ImageCompressor) {
            compressor = new ImageCompressor();
            initializeAdvancedFeatures();
        }
    }, 100);
});

function initializeAdvancedFeatures() {
    // Initialize custom resize inputs
    const resizeSelect = document.getElementById('resizeSelect');
    const customSizeInputs = document.getElementById('customSizeInputs');
    
    if (resizeSelect) {
        resizeSelect.addEventListener('change', (e) => {
            if (e.target.value === 'custom') {
                customSizeInputs.style.display = 'block';
            } else {
                customSizeInputs.style.display = 'none';
            }
        });
    }

    // Initialize quality presets
    initializeQualityPresets();
    
    // Initialize progress tracking
    initializeProgressTracking();
    
    // Initialize advanced compression options
    initializeAdvancedOptions();
}

function setQuality(value) {
    const qualitySlider = document.getElementById('qualitySlider');
    const qualityValue = document.getElementById('qualityValue');
    
    if (qualitySlider && qualityValue) {
        qualitySlider.value = value;
        qualityValue.textContent = value + '%';
        
        // Add visual feedback
        const presetBtns = document.querySelectorAll('.preset-btn');
        presetBtns.forEach(btn => btn.classList.remove('active'));
        event.target.classList.add('active');
    }
}

function initializeQualityPresets() {
    const qualitySlider = document.getElementById('qualitySlider');
    const qualityValue = document.getElementById('qualityValue');
    
    if (qualitySlider && qualityValue) {
        qualitySlider.addEventListener('input', (e) => {
            qualityValue.textContent = e.target.value + '%';
            updateQualityIndicator(e.target.value);
        });
    }
}

function updateQualityIndicator(quality) {
    const qualityValue = document.getElementById('qualityValue');
    if (qualityValue) {
        // Add color coding based on quality
        if (quality >= 90) {
            qualityValue.style.color = '#27ae60'; // Green for high quality
        } else if (quality >= 70) {
            qualityValue.style.color = '#f39c12'; // Orange for medium quality
        } else {
            qualityValue.style.color = '#e74c3c'; // Red for low quality
        }
    }
}

function initializeProgressTracking() {
    // Override the original compress method to add progress tracking
    if (compressor && compressor.compressAllImages) {
        const originalCompress = compressor.compressAllImages.bind(compressor);
        
        compressor.compressAllImages = async function() {
            showProgressSection();
            updateProgress(0, 'Starting compression...');
            
            try {
                await originalCompress();
                updateProgress(100, 'Compression completed!');
                showCompressionSummary();
                setTimeout(hideProgressSection, 2000);
            } catch (error) {
                updateProgress(0, 'Compression failed!');
                setTimeout(hideProgressSection, 3000);
            }
        };
    }
}

function showProgressSection() {
    const progressSection = document.getElementById('progressSection');
    if (progressSection) {
        progressSection.style.display = 'block';
        progressSection.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
}

function hideProgressSection() {
    const progressSection = document.getElementById('progressSection');
    if (progressSection) {
        progressSection.style.display = 'none';
    }
}

function updateProgress(percentage, text) {
    const progressFill = document.getElementById('progressFill');
    const progressText = document.getElementById('progressText');
    
    if (progressFill) {
        progressFill.style.width = percentage + '%';
    }
    
    if (progressText) {
        progressText.textContent = text;
    }
}

function togglePreviewMode() {
    const container = document.getElementById('imagePreviewContainer');
    const modeIcon = document.getElementById('previewModeIcon');
    const modeText = document.getElementById('previewModeText');
    
    if (previewMode === 'grid') {
        previewMode = 'list';
        container.classList.add('preview-list');
        modeIcon.textContent = '📋';
        modeText.textContent = 'List View';
    } else {
        previewMode = 'grid';
        container.classList.remove('preview-list');
        modeIcon.textContent = '🖼️';
        modeText.textContent = 'Grid View';
    }
}

function sortImages(criteria) {
    if (!compressor || !compressor.images) return;
    
    const container = document.getElementById('imagePreviewContainer');
    const imageItems = Array.from(container.children);
    
    imageItems.sort((a, b) => {
        const indexA = parseInt(a.dataset.imageId);
        const indexB = parseInt(b.dataset.imageId);
        
        if (criteria === 'name') {
            const nameA = compressor.images[indexA]?.name || '';
            const nameB = compressor.images[indexB]?.name || '';
            return nameA.localeCompare(nameB);
        } else if (criteria === 'size') {
            const sizeA = compressor.images[indexA]?.originalSize || 0;
            const sizeB = compressor.images[indexB]?.originalSize || 0;
            return sizeB - sizeA; // Descending order
        }
        
        return 0;
    });
    
    // Re-append sorted items
    imageItems.forEach(item => container.appendChild(item));
    
    // Add visual feedback
    const sortBtn = event.target;
    const allSortBtns = document.querySelectorAll('.control-btn');
    allSortBtns.forEach(btn => btn.classList.remove('active'));
    sortBtn.classList.add('active');
}

function showCompressionSummary() {
    if (!compressor || !compressor.images || !compressor.compressedImages) return;
    
    const summarySection = document.getElementById('compressionSummary');
    if (!summarySection) return;
    
    let totalOriginalSize = 0;
    let totalCompressedSize = 0;
    let processedImages = 0;
    
    compressor.images.forEach((image, index) => {
        totalOriginalSize += image.originalSize;
        const compressed = compressor.compressedImages[index];
        if (compressed) {
            totalCompressedSize += compressed.compressedSize;
            processedImages++;
        }
    });
    
    const totalSavings = totalOriginalSize > 0 ? 
        ((totalOriginalSize - totalCompressedSize) / totalOriginalSize * 100) : 0;
    
    // Update summary display
    document.getElementById('totalImages').textContent = processedImages;
    document.getElementById('originalTotalSize').textContent = formatFileSize(totalOriginalSize);
    document.getElementById('compressedTotalSize').textContent = formatFileSize(totalCompressedSize);
    document.getElementById('totalSavings').textContent = totalSavings.toFixed(1) + '%';
    
    // Show summary with animation
    summarySection.style.display = 'block';
    summarySection.style.opacity = '0';
    summarySection.style.transform = 'translateY(20px)';
    
    setTimeout(() => {
        summarySection.style.transition = 'all 0.5s ease';
        summarySection.style.opacity = '1';
        summarySection.style.transform = 'translateY(0)';
    }, 100);
}

function initializeAdvancedOptions() {
    // Add event listeners for advanced options
    const removeMetadata = document.getElementById('removeMetadata');
    const progressiveJPEG = document.getElementById('progressiveJPEG');
    const optimizePNG = document.getElementById('optimizePNG');
    const autoRotate = document.getElementById('autoRotate');
    
    // Add visual feedback for checkbox changes
    [removeMetadata, progressiveJPEG, optimizePNG, autoRotate].forEach(checkbox => {
        if (checkbox) {
            checkbox.addEventListener('change', (e) => {
                const label = e.target.parentElement;
                if (e.target.checked) {
                    label.style.color = '#e74c3c';
                    label.style.fontWeight = '600';
                } else {
                    label.style.color = '';
                    label.style.fontWeight = '';
                }
            });
        }
    });
}

// Enhanced file size formatting
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    const size = parseFloat((bytes / Math.pow(k, i)).toFixed(2));
    return size + ' ' + sizes[i];
}

// Add keyboard shortcuts
document.addEventListener('keydown', (e) => {
    // Ctrl/Cmd + U: Upload files
    if ((e.ctrlKey || e.metaKey) && e.key === 'u') {
        e.preventDefault();
        document.getElementById('fileInput').click();
    }
    
    // Ctrl/Cmd + Enter: Compress all
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
        e.preventDefault();
        if (compressor && compressor.images.length > 0) {
            compressor.compressAllImages();
        }
    }
    
    // Ctrl/Cmd + D: Download all
    if ((e.ctrlKey || e.metaKey) && e.key === 'd') {
        e.preventDefault();
        if (compressor && compressor.compressedImages.some(img => img)) {
            compressor.downloadAllImages();
        }
    }
    
    // Escape: Clear all
    if (e.key === 'Escape') {
        if (compressor && compressor.images.length > 0) {
            if (confirm('Are you sure you want to clear all images?')) {
                compressor.clearAllImages();
            }
        }
    }
});

// Add drag and drop enhancement
document.addEventListener('DOMContentLoaded', () => {
    const body = document.body;
    let dragCounter = 0;
    
    body.addEventListener('dragenter', (e) => {
        e.preventDefault();
        dragCounter++;
        body.classList.add('drag-active');
    });
    
    body.addEventListener('dragleave', (e) => {
        e.preventDefault();
        dragCounter--;
        if (dragCounter === 0) {
            body.classList.remove('drag-active');
        }
    });
    
    body.addEventListener('drop', (e) => {
        e.preventDefault();
        dragCounter = 0;
        body.classList.remove('drag-active');
    });
});

// Add CSS for drag and drop enhancement
const style = document.createElement('style');
style.textContent = `
    .drag-active {
        background: linear-gradient(135deg, rgba(231, 76, 60, 0.1), rgba(231, 76, 60, 0.05));
    }
    
    .drag-active::before {
        content: '📁 Drop your images anywhere to upload';
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(231, 76, 60, 0.95);
        color: white;
        padding: 20px 40px;
        border-radius: 15px;
        font-size: 1.2rem;
        font-weight: 600;
        z-index: 10000;
        pointer-events: none;
        backdrop-filter: blur(10px);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }
    
    .preset-btn.active {
        background: #e74c3c !important;
        color: white !important;
    }
    
    .control-btn.active {
        background: #e74c3c !important;
        color: white !important;
    }
`;
document.head.appendChild(style);

// Add tooltips for better UX
function addTooltips() {
    const tooltipElements = [
        { selector: '#removeMetadata', text: 'Removes camera and location data to reduce file size' },
        { selector: '#progressiveJPEG', text: 'Creates images that load progressively for better web performance' },
        { selector: '#optimizePNG', text: 'Applies additional PNG optimization techniques' },
        { selector: '#autoRotate', text: 'Automatically rotates images based on camera orientation data' }
    ];
    
    tooltipElements.forEach(({ selector, text }) => {
        const element = document.querySelector(selector);
        if (element) {
            element.title = text;
        }
    });
}

// Initialize tooltips when DOM is ready
document.addEventListener('DOMContentLoaded', addTooltips);



// Image comparison modal
function showImageComparison(imageIndex) {
    if (!compressor || !compressor.images[imageIndex] || !compressor.compressedImages[imageIndex]) {
        return;
    }

    const original = compressor.images[imageIndex];
    const compressed = compressor.compressedImages[imageIndex];

    // Set modal content
    document.getElementById('originalImage').src = URL.createObjectURL(original.file);
    document.getElementById('compressedImage').src = URL.createObjectURL(compressed.blob);

    document.getElementById('originalSize').textContent = formatFileSize(original.originalSize);
    document.getElementById('compressedSize').textContent = formatFileSize(compressed.compressedSize);

    // Calculate dimensions (this would need to be enhanced to get actual dimensions)
    document.getElementById('originalDimensions').textContent = 'Loading...';

    const savings = ((original.originalSize - compressed.compressedSize) / original.originalSize * 100).toFixed(1);
    document.getElementById('compressionSavings').textContent = savings + '%';

    // Show modal
    document.getElementById('comparisonModal').style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

function closeComparisonModal() {
    document.getElementById('comparisonModal').style.display = 'none';
    document.body.style.overflow = 'auto';

    // Clean up object URLs
    const originalImg = document.getElementById('originalImage');
    const compressedImg = document.getElementById('compressedImage');

    if (originalImg.src.startsWith('blob:')) {
        URL.revokeObjectURL(originalImg.src);
    }
    if (compressedImg.src.startsWith('blob:')) {
        URL.revokeObjectURL(compressedImg.src);
    }
}

// Notification system
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;

    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 25px;
        border-radius: 10px;
        color: white;
        font-weight: 600;
        z-index: 10001;
        animation: slideInRight 0.3s ease-out;
        max-width: 300px;
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    `;

    // Set background color based on type
    switch (type) {
        case 'success':
            notification.style.background = 'linear-gradient(135deg, #27ae60, #229954)';
            break;
        case 'error':
            notification.style.background = 'linear-gradient(135deg, #e74c3c, #c0392b)';
            break;
        case 'warning':
            notification.style.background = 'linear-gradient(135deg, #f39c12, #e67e22)';
            break;
        default:
            notification.style.background = 'linear-gradient(135deg, #3498db, #2980b9)';
    }

    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease-in';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// Add notification animations to CSS
const notificationStyle = document.createElement('style');
notificationStyle.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
`;
document.head.appendChild(notificationStyle);

// Enhanced image item creation with comparison button
function enhanceImageItem(imageElement, imageIndex) {
    const actionsDiv = imageElement.querySelector('.image-actions');
    if (actionsDiv && compressor.compressedImages[imageIndex]) {
        const compareBtn = document.createElement('button');
        compareBtn.className = 'btn btn-info btn-small';
        compareBtn.innerHTML = '🔍 Compare';
        compareBtn.addEventListener('click', () => showImageComparison(imageIndex));
        actionsDiv.appendChild(compareBtn);
    }
}

// Close modal when clicking outside
document.addEventListener('click', (e) => {
    const modal = document.getElementById('comparisonModal');
    if (e.target === modal) {
        closeComparisonModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        const modal = document.getElementById('comparisonModal');
        if (modal.style.display === 'flex') {
            closeComparisonModal();
        }
    }
});
