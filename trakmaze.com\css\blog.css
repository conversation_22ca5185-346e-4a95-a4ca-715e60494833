/* Blog Styles */

/* Blog Header */
.blog-header {
    text-align: center;
    margin-bottom: 50px;
    color: white;
    padding: 50px 0;
    background: linear-gradient(135deg, #2c3e50 0%, #e74c3c 50%, #2c3e50 100%);
    border-radius: 20px;
    margin-top: 20px;
}

.blog-header h1 {
    font-size: 3.5rem;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    font-weight: 700;
}

.blog-header p {
    font-size: 1.3rem;
    opacity: 0.9;
    margin-bottom: 30px;
}

.blog-stats {
    display: flex;
    justify-content: center;
    gap: 40px;
    flex-wrap: wrap;
}

.stat-item {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2rem;
    font-weight: 700;
    color: white;
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: 5px;
}

/* Featured Article */
.featured-section {
    margin-bottom: 50px;
}

.featured-section h2 {
    color: #333;
    font-size: 2rem;
    margin-bottom: 25px;
    font-weight: 600;
}

.featured-article {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
}

.featured-article:hover {
    border-color: #e74c3c;
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.article-image {
    position: relative;
    overflow: hidden;
}

.article-image img {
    width: 100%;
    height: 300px;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.featured-article:hover .article-image img {
    transform: scale(1.05);
}

.article-category {
    position: absolute;
    top: 15px;
    left: 15px;
    background: #e74c3c;
    color: white;
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 600;
}

.article-content {
    padding: 30px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.article-content h3 {
    margin-bottom: 15px;
    font-size: 1.5rem;
    line-height: 1.3;
}

.article-content h3 a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.article-content h3 a:hover {
    color: #e74c3c;
}

.article-excerpt {
    color: #666;
    line-height: 1.6;
    margin-bottom: 20px;
    flex-grow: 1;
}

.article-meta {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
    font-size: 0.9rem;
    color: #888;
    flex-wrap: wrap;
}

.read-more-btn {
    background: #e74c3c;
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    align-self: flex-start;
}

.read-more-btn:hover {
    background: #c0392b;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

/* Categories */
.categories-section {
    margin-bottom: 40px;
}

.categories-section h2 {
    color: #333;
    font-size: 1.8rem;
    margin-bottom: 20px;
    font-weight: 600;
}

.category-filters {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.category-btn {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    color: #666;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
}

.category-btn:hover,
.category-btn.active {
    background: #e74c3c;
    border-color: #e74c3c;
    color: white;
}

/* Articles Grid */
.articles-section h2 {
    color: #333;
    font-size: 2rem;
    margin-bottom: 30px;
    font-weight: 600;
}

.articles-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.article-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
}

.article-card:hover {
    border-color: #e74c3c;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.article-card .article-image {
    height: 200px;
}

.article-card .article-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.article-card .article-content {
    padding: 25px;
}

.article-card h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
}

.article-card .article-excerpt {
    margin-bottom: 15px;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Load More */
.load-more-section {
    text-align: center;
    margin: 40px 0;
}

.load-more-btn {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
}

.load-more-btn:hover {
    background: #c0392b;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

/* Newsletter */
.newsletter-section {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    color: white;
    padding: 50px;
    border-radius: 20px;
    text-align: center;
    margin: 50px 0;
}

.newsletter-section h2 {
    font-size: 2.2rem;
    margin-bottom: 15px;
    font-weight: 700;
}

.newsletter-section p {
    font-size: 1.1rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.newsletter-form {
    display: flex;
    max-width: 500px;
    margin: 0 auto 15px;
    gap: 15px;
}

.newsletter-form input {
    flex: 1;
    padding: 15px 20px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
}

.newsletter-form button {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.newsletter-form button:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

.newsletter-note {
    font-size: 0.9rem;
    opacity: 0.7;
    margin: 0;
}

/* Article Page Styles */
.breadcrumb {
    margin: 20px 0;
    font-size: 0.9rem;
    color: #666;
}

.breadcrumb a {
    color: #e74c3c;
    text-decoration: none;
}

.breadcrumb a:hover {
    text-decoration: underline;
}

.separator {
    margin: 0 10px;
    color: #ccc;
}

.article-header {
    text-align: center;
    margin-bottom: 40px;
    padding: 40px 0;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 20px;
}

.article-category-badge {
    background: #e74c3c;
    color: white;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 600;
    display: inline-block;
    margin-bottom: 20px;
}

.article-header h1 {
    color: #333;
    font-size: 2.5rem;
    margin-bottom: 25px;
    font-weight: 700;
    line-height: 1.2;
}

.article-meta {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    font-size: 0.9rem;
    color: #666;
}

.meta-item {
    display: flex;
    align-items: center;
    gap: 5px;
}

.meta-label {
    font-weight: 600;
    color: #333;
}

.article-content {
    max-width: 800px;
    margin: 0 auto;
}

.article-image-container {
    margin-bottom: 40px;
    text-align: center;
}

.article-image-container img {
    max-width: 100%;
    height: auto;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.article-body {
    line-height: 1.8;
    font-size: 1.1rem;
    color: #444;
}

.article-body h2 {
    color: #333;
    font-size: 1.8rem;
    margin: 40px 0 20px;
    font-weight: 600;
}

.article-body h3 {
    color: #333;
    font-size: 1.4rem;
    margin: 30px 0 15px;
    font-weight: 600;
}

.article-body p {
    margin-bottom: 20px;
}

.article-body ul, .article-body ol {
    margin-bottom: 20px;
    padding-left: 30px;
}

.article-body li {
    margin-bottom: 8px;
}

.article-body blockquote {
    background: #f8f9fa;
    border-left: 4px solid #e74c3c;
    padding: 20px;
    margin: 30px 0;
    font-style: italic;
    border-radius: 0 10px 10px 0;
}

.article-body code {
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 0.9em;
}

.article-body pre {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    overflow-x: auto;
    margin: 20px 0;
}

.article-tags {
    margin: 40px 0;
    text-align: center;
}

.tag {
    background: #f8f9fa;
    color: #666;
    padding: 6px 15px;
    border-radius: 15px;
    font-size: 0.85rem;
    margin: 5px;
    display: inline-block;
    border: 1px solid #e9ecef;
}

.share-section {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    margin: 40px 0;
}

.share-section h3 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.3rem;
}

.share-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.share-btn {
    background: white;
    border: 2px solid #e9ecef;
    color: #666;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
}

.share-btn:hover {
    border-color: #e74c3c;
    color: #e74c3c;
    transform: translateY(-2px);
}

.related-articles {
    margin: 50px 0;
}

.related-articles h2 {
    color: #333;
    font-size: 2rem;
    margin-bottom: 30px;
    font-weight: 600;
    text-align: center;
}

.related-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.comments-section {
    background: #f8f9fa;
    padding: 40px;
    border-radius: 15px;
    text-align: center;
    margin: 40px 0;
}

.comments-section h2 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.8rem;
}

.comments-notice p {
    color: #666;
    margin-bottom: 20px;
    font-size: 1.1rem;
}

.social-links {
    display: flex;
    justify-content: center;
    gap: 15px;
}

.social-link {
    color: #e74c3c;
    text-decoration: none;
    padding: 8px 16px;
    border: 1px solid #e74c3c;
    border-radius: 20px;
    transition: all 0.3s ease;
}

.social-link:hover {
    background: #e74c3c;
    color: white;
}

.back-to-blog {
    text-align: center;
    margin: 40px 0;
}

.back-btn {
    background: #e74c3c;
    color: white;
    padding: 12px 25px;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 10px;
}

.back-btn:hover {
    background: #c0392b;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

.loading-content {
    text-align: center;
    padding: 60px 0;
    color: #666;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #e74c3c;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .blog-header h1 {
        font-size: 2.5rem;
    }

    .blog-stats {
        gap: 20px;
    }

    .featured-article {
        grid-template-columns: 1fr;
    }

    .article-image {
        height: 250px;
    }

    .category-filters {
        justify-content: center;
    }

    .articles-grid {
        grid-template-columns: 1fr;
    }

    .newsletter-form {
        flex-direction: column;
    }

    .newsletter-section {
        padding: 30px 20px;
    }

    .article-header h1 {
        font-size: 2rem;
    }

    .article-meta {
        flex-direction: column;
        gap: 10px;
    }

    .share-buttons {
        flex-direction: column;
        align-items: center;
    }

    .related-grid {
        grid-template-columns: 1fr;
    }
}
