/* Compressor Page Specific Styles */

/* Header Styles */
.compressor-header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
    padding: 40px 0;
    background: linear-gradient(135deg, #2c3e50 0%, #e74c3c 50%, #2c3e50 100%);
    border-radius: 20px;
    margin-top: 20px;
}

.compressor-header h1 {
    font-size: 3.5rem;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    font-weight: 700;
}

.compressor-header p {
    font-size: 1.3rem;
    opacity: 0.9;
    margin-bottom: 30px;
}

.header-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.stat-badge {
    display: flex;
    align-items: center;
    gap: 10px;
    background: rgba(255, 255, 255, 0.15);
    padding: 15px 25px;
    border-radius: 30px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
}

.stat-badge:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-3px);
}

.stat-icon {
    font-size: 1.4rem;
}

/* Quick Start Section */
.quick-start-section {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 30px;
    border-radius: 15px;
    margin-bottom: 40px;
    border: 2px solid #e74c3c;
}

.quick-start-section h2 {
    text-align: center;
    color: #333;
    margin-bottom: 25px;
    font-size: 1.8rem;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.quick-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    padding: 20px;
    background: white;
    border: 2px solid #e74c3c;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    color: #333;
}

.quick-btn:hover {
    background: #e74c3c;
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(231, 76, 60, 0.3);
}

.quick-icon {
    font-size: 2rem;
}

/* Enhanced Upload Section */
.upload-area {
    border: 3px dashed #e74c3c;
    border-radius: 15px;
    padding: 50px 30px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: linear-gradient(135deg, #fdf2f2, #fce4e4);
    position: relative;
    overflow: hidden;
}

.upload-area::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(231, 76, 60, 0.1), transparent);
    transform: rotate(45deg);
    transition: all 0.3s ease;
    opacity: 0;
}

.upload-area:hover::before {
    opacity: 1;
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }

    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }
}

.upload-features {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 20px 0;
    flex-wrap: wrap;
}

.upload-feature {
    background: rgba(231, 76, 60, 0.1);
    padding: 8px 15px;
    border-radius: 20px;
    font-size: 0.9rem;
    color: #e74c3c;
    font-weight: 600;
}

/* Settings Section */
.settings-section {
    margin: 40px 0;
}

.settings-section h2 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
    font-size: 2rem;
}

.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 25px;
}

.setting-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
}

.setting-card:hover {
    border-color: #e74c3c;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.setting-card h3 {
    color: #e74c3c;
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
}

.quality-presets {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.preset-btn {
    padding: 8px 15px;
    background: #f8f9fa;
    border: 1px solid #e74c3c;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    color: #e74c3c;
}

.preset-btn:hover {
    background: #e74c3c;
    color: white;
}

.format-info {
    font-size: 0.85rem;
    color: #666;
    margin-top: 10px;
    font-style: italic;
}

#customSizeInputs {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
}

#customSizeInputs input[type="number"] {
    width: 100px;
    padding: 8px;
    margin: 5px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

/* Batch Operations */
#batchPrefix,
#batchSuffix {
    width: 100%;
    padding: 10px;
    margin: 5px 0;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 0.9rem;
}

#batchPrefix:focus,
#batchSuffix:focus {
    outline: none;
    border-color: #e74c3c;
    box-shadow: 0 0 5px rgba(231, 76, 60, 0.3);
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 10000;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(5px);
}

.modal-content {
    background: white;
    border-radius: 20px;
    max-width: 90vw;
    max-height: 90vh;
    overflow: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-50px);
    }

    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 25px 30px;
    border-bottom: 2px solid #f0f0f0;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    border-radius: 20px 20px 0 0;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.modal-close {
    background: none;
    border: none;
    font-size: 2rem;
    color: white;
    cursor: pointer;
    padding: 0;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

.modal-body {
    padding: 30px;
}

/* Comparison Styles */
.comparison-container {
    display: grid;
    grid-template-columns: 1fr auto 1fr;
    gap: 30px;
    align-items: center;
}

.comparison-side {
    text-align: center;
}

.comparison-side h4 {
    color: #333;
    margin-bottom: 20px;
    font-size: 1.3rem;
    font-weight: 600;
}

.comparison-side img {
    max-width: 100%;
    max-height: 400px;
    border-radius: 12px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.comparison-side img:hover {
    transform: scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.comparison-divider {
    display: flex;
    align-items: center;
    justify-content: center;
}

.vs-badge {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 15px 20px;
    border-radius: 50%;
    font-weight: 700;
    font-size: 1.2rem;
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
    animation: pulse 2s infinite;
}

@keyframes pulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
    }
}

.image-details {
    margin-top: 15px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.image-details p {
    margin: 5px 0;
    color: #666;
    font-size: 0.9rem;
}

.image-details span {
    font-weight: 600;
    color: #333;
}

/* Progress Section */
.progress-section {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    padding: 30px;
    border-radius: 15px;
    margin: 30px 0;
    color: white;
}

.progress-container {
    max-width: 600px;
    margin: 0 auto;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    overflow: hidden;
    margin-bottom: 15px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #fff, #f0f0f0);
    width: 0%;
    transition: width 0.3s ease;
    border-radius: 10px;
}

.progress-text {
    text-align: center;
    font-size: 1.1rem;
    font-weight: 600;
}

/* Enhanced Preview Section */
.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.preview-controls {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.control-btn {
    padding: 8px 15px;
    background: #f8f9fa;
    border: 1px solid #e74c3c;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
    color: #e74c3c;
}

.control-btn:hover {
    background: #e74c3c;
    color: white;
}

.preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(100%, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.preview-list .preview-grid {
    grid-template-columns: 1fr;
}

/* Enhanced Image Items */
.image-item {
    background: white;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.image-item:hover {
    border-color: #e74c3c;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.image-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 3px;
    background: linear-gradient(90deg, transparent, #e74c3c, transparent);
    transition: left 0.5s ease;
}

.image-item:hover::before {
    left: 100%;
}

/* Batch Actions */
.batch-actions {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 15px;
    margin: 30px 0;
    border: 2px solid #e9ecef;
}

.action-group {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.btn {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 15px 30px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

.btn-icon {
    font-size: 1.2rem;
}

/* Compression Summary */
.compression-summary {
    background: linear-gradient(135deg, #27ae60, #229954);
    padding: 25px;
    border-radius: 15px;
    color: white;
    margin: 30px 0;
}

.compression-summary h3 {
    text-align: center;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.summary-stat {
    text-align: center;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    backdrop-filter: blur(10px);
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    opacity: 0.9;
    margin-bottom: 5px;
}

.stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: 700;
}

/* Tutorial Section */
.tutorial-section {
    margin: 50px 0;
    padding: 40px;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 20px;
    border: 2px solid #e74c3c;
}

.tutorial-section h2 {
    text-align: center;
    color: #333;
    margin-bottom: 40px;
    font-size: 2.2rem;
    font-weight: 700;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.tutorial-content {
    max-width: 1000px;
    margin: 0 auto;
}

.tutorial-step {
    background: white;
    margin-bottom: 25px;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    border: 2px solid #f0f0f0;
    transition: all 0.3s ease;
}

.tutorial-step:hover {
    border-color: #e74c3c;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.step-header {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    padding: 20px 30px;
    display: flex;
    align-items: center;
    gap: 20px;
}

.step-number {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.step-header h3 {
    margin: 0;
    font-size: 1.4rem;
    font-weight: 600;
}

.step-content {
    padding: 30px;
}

.step-content p {
    color: #444;
    line-height: 1.7;
    margin-bottom: 15px;
    font-size: 1rem;
}

.step-content p:last-child {
    margin-bottom: 0;
}

.step-content strong {
    color: #e74c3c;
    font-weight: 600;
}

/* Tips Section */
.tips-section {
    margin: 50px 0;
    padding: 40px 0;
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border-radius: 20px;
}

.tips-section h2 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
    font-size: 2rem;
}

.tips-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    padding: 0 30px;
}

.tip-card {
    background: white;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.tip-card:hover {
    border-color: #e74c3c;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.tip-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    display: block;
}

.tip-card h4 {
    color: #333;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.tip-card p {
    color: #666;
    line-height: 1.6;
    font-size: 0.95rem;
}

/* Enhanced Image Item Styles */
.image-item .image-preview {
    width: 120px;
    height: 120px;
    object-fit: cover;
    border-radius: 12px;
    margin-right: 20px;
    border: 3px solid #f0f0f0;
    transition: all 0.3s ease;
}

.image-item:hover .image-preview {
    border-color: #e74c3c;
    transform: scale(1.05);
}

.image-info h4 {
    color: #333;
    font-size: 1.1rem;
    margin-bottom: 8px;
    font-weight: 600;
}

.image-info p {
    color: #666;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.compression-status {
    font-weight: 600;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.85rem;
    display: inline-block;
    margin-top: 5px;
}

.compression-result {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.compression-warning {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.compression-info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.image-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.85rem;
    border-radius: 15px;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        transform: translateX(-20px);
        opacity: 0;
    }

    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.bounce-in {
    animation: bounceIn 0.6s ease-out;
}

@keyframes bounceIn {
    0% {
        transform: scale(0.3);
        opacity: 0;
    }

    50% {
        transform: scale(1.05);
    }

    70% {
        transform: scale(0.9);
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px;
    backdrop-filter: blur(5px);
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #e74c3c;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Success States */
.success-checkmark {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: #27ae60;
    position: relative;
    margin: 0 auto 15px;
}

.success-checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 30px;
    font-weight: bold;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .compressor-header h1 {
        font-size: 2.5rem;
    }

    .header-stats {
        flex-direction: column;
        gap: 15px;
    }

    .quick-actions {
        grid-template-columns: repeat(2, 1fr);
    }

    .settings-grid {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
    }

    .preview-controls {
        justify-content: center;
    }

    .action-group {
        flex-direction: column;
    }

    .summary-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .tips-grid {
        grid-template-columns: 1fr;
        padding: 0 20px;
    }

    .image-item {
        flex-direction: column;
        text-align: center;
    }

    .image-item .image-preview {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .quick-btn {
        padding: 15px;
    }

    .quick-icon {
        font-size: 1.5rem;
    }
}

@media (max-width: 480px) {
    .compressor-header {
        padding: 30px 20px;
    }

    .compressor-header h1 {
        font-size: 2rem;
    }

    .quick-actions {
        grid-template-columns: 1fr;
    }

    .stat-badge {
        padding: 12px 20px;
    }

    .setting-card {
        padding: 20px;
    }

    .summary-stats {
        grid-template-columns: 1fr;
    }

    .comparison-container {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .comparison-divider {
        order: 2;
    }

    .vs-badge {
        padding: 10px 15px;
        font-size: 1rem;
    }

    .modal-content {
        max-width: 95vw;
        margin: 10px;
    }

    .modal-header {
        padding: 20px;
    }

    .modal-body {
        padding: 20px;
    }
}

/* Additional button styles */
.btn-info {
    background: #17a2b8;
    color: white;
}

.btn-info:hover {
    background: #138496;
    transform: translateY(-2px);
}

/* Enhanced loading states */
.image-item.processing {
    position: relative;
    pointer-events: none;
}

.image-item.processing::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.image-item.processing::before {
    content: '⚡ Processing...';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #e74c3c;
    color: white;
    padding: 10px 20px;
    border-radius: 20px;
    font-weight: 600;
    z-index: 1;
    animation: pulse 1.5s infinite;
}

/* Success state */
.image-item.completed {
    border-color: #27ae60;
    background: linear-gradient(135deg, #f8fff8, #e8f5e8);
}

.image-item.completed::before {
    content: '✅';
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 1.5rem;
    z-index: 1;
}

/* Error state */
.image-item.error {
    border-color: #e74c3c;
    background: linear-gradient(135deg, #fff8f8, #f8e8e8);
}

.image-item.error::before {
    content: '❌';
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 1.5rem;
    z-index: 1;
}

