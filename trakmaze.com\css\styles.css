/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 50%, #2c3e50 100%);
    min-height: 100vh;
    padding-top: 80px;
    /* Add space for fixed navigation */
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Top Navigation Styles */
.top-navigation {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(44, 62, 80, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-brand {
    display: flex;
    align-items: center;
}

.brand-link {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: white;
    font-weight: 600;
    font-size: 1.2rem;
    transition: all 0.3s ease;
}

.brand-link:hover {
    color: #e74c3c;
    transform: translateY(-1px);
}

.brand-icon {
    width: 28px;
    height: 28px;
    margin-right: 10px;
    color: #e74c3c;
}

.brand-text {
    font-weight: 700;
}

.nav-menu {
    display: flex;
    align-items: center;
}

.nav-links {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: 30px;
}

.nav-link {
    color: white;
    text-decoration: none;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
    opacity: 0.9;
}

.nav-link:hover {
    color: #e74c3c;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    opacity: 1;
}

.nav-link.active {
    color: #e74c3c;
    background: rgba(231, 76, 60, 0.1);
    border: 1px solid rgba(231, 76, 60, 0.3);
    opacity: 1;
}

.nav-toggle {
    display: none;
    flex-direction: column;
    cursor: pointer;
    padding: 5px;
}

.nav-toggle-bar {
    width: 25px;
    height: 3px;
    background: white;
    margin: 3px 0;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.nav-toggle:hover .nav-toggle-bar {
    background: #e74c3c;
}

/* Header styles */
header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

header h1 {
    font-size: 3rem;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

header p {
    font-size: 1.2rem;
    opacity: 0.9;
    margin-bottom: 30px;
}

/* Hero Features */
.hero-features {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
    margin-top: 20px;
}

.feature-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(255, 255, 255, 0.1);
    padding: 12px 20px;
    border-radius: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    font-weight: 600;
    transition: all 0.3s ease;
}

.feature-badge:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
}

.badge-icon {
    font-size: 1.2rem;
}

/* Main content */
main {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

/* Upload section */
.upload-section {
    margin-bottom: 30px;
}

.upload-area {
    border: 3px dashed #e74c3c;
    border-radius: 10px;
    padding: 60px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #fdf2f2;
}

.upload-area:hover {
    border-color: #c0392b;
    background: #fce4e4;
}

.upload-area.dragover {
    border-color: #c0392b;
    background: #f8d7d7;
    transform: scale(1.02);
}

.upload-icon {
    width: 64px;
    height: 64px;
    color: #e74c3c;
    margin-bottom: 20px;
}

.upload-content h3 {
    font-size: 1.5rem;
    margin-bottom: 10px;
    color: #333;
}

.upload-content p {
    color: #666;
    margin-bottom: 5px;
}

.file-types {
    font-size: 0.9rem;
    color: #999;
}

.compression-tip {
    font-size: 0.85rem;
    color: #e74c3c;
    font-style: italic;
    margin-top: 5px;
}

/* Compression settings */
.compression-settings {
    display: flex;
    gap: 30px;
    margin-bottom: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    flex-wrap: wrap;
}

.setting-group {
    flex: 1;
    min-width: 200px;
}

.setting-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #333;
}

.slider-container {
    display: flex;
    align-items: center;
    gap: 15px;
}

#qualitySlider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
}

#qualitySlider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #e74c3c;
    cursor: pointer;
}

#qualitySlider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #e74c3c;
    cursor: pointer;
    border: none;
}

#qualityValue {
    font-weight: 600;
    color: #e74c3c;
    min-width: 40px;
}

#formatSelect,
#resizeSelect {
    width: 100%;
    padding: 10px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    background: white;
}

#formatSelect:focus,
#resizeSelect:focus {
    outline: none;
    border-color: #e74c3c;
}

/* Image preview section */
.image-preview-section {
    margin-top: 30px;
}

.image-preview-section h3 {
    margin-bottom: 20px;
    color: #333;
}

#imagePreviewContainer {
    margin-bottom: 30px;
}

.image-item {
    display: flex;
    align-items: center;
    padding: 20px;
    border: 1px solid #ddd;
    border-radius: 10px;
    margin-bottom: 15px;
    background: #fafafa;
}

.image-preview {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 8px;
    margin-right: 20px;
}

.image-info {
    flex: 1;
}

.image-info h4 {
    margin-bottom: 5px;
    color: #333;
}

.image-info p {
    color: #666;
    margin-bottom: 3px;
}

.compression-result {
    color: #28a745;
    font-weight: 600;
}

.compression-warning {
    color: #dc3545;
    font-weight: 600;
}

.compression-info {
    color: #6c757d;
    font-weight: 600;
}

.image-actions {
    display: flex;
    gap: 10px;
}

/* Buttons */
.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-primary {
    background: #e74c3c;
    color: white;
}

.btn-primary:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

.btn-primary:disabled {
    background: #27ae60 !important;
    cursor: not-allowed !important;
    transform: none !important;
    opacity: 0.8;
}

.btn-primary:disabled:hover {
    background: #27ae60 !important;
    transform: none !important;
    opacity: 0.8;
}

.btn-secondary {
    background: #27ae60;
    color: white;
}

.btn-secondary:hover {
    background: #229954;
    transform: translateY(-2px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.btn-small {
    padding: 8px 16px;
    font-size: 0.9rem;
}

.action-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.reset-instruction {
    margin-top: 20px;
    padding: 15px;
    background: #d4edda;
    border: 1px solid #c3e6cb;
    border-radius: 8px;
    text-align: center;
}

.reset-instruction p {
    color: #155724;
    font-size: 0.95rem;
    font-weight: 600;
    margin: 0;
}

.compression-info {
    margin-top: 20px;
    text-align: center;
}

.compression-info p {
    color: #e74c3c;
    font-size: 0.9rem;
    font-style: italic;
    margin: 0;
}

/* Features Section */
.features-section {
    margin-top: 50px;
    padding-top: 40px;
    border-top: 2px solid #f0f0f0;
}

.features-section h2 {
    text-align: center;
    color: #333;
    font-size: 2.2rem;
    margin-bottom: 40px;
    font-weight: 700;
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 30px;
    margin-bottom: 40px;
}

.feature-card {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    background: #fff;
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 20px;
    display: block;
}

.feature-card h3 {
    color: #333;
    font-size: 1.3rem;
    margin-bottom: 15px;
    font-weight: 600;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
    font-size: 0.95rem;
}

/* How It Works Section */
.how-it-works-section {
    margin-top: 50px;
    padding-top: 40px;
    border-top: 2px solid #f0f0f0;
}

.how-it-works-section h2 {
    text-align: center;
    color: #333;
    font-size: 2.2rem;
    margin-bottom: 40px;
    font-weight: 700;
}

.steps-container {
    display: flex;
    flex-direction: column;
    gap: 30px;
    max-width: 800px;
    margin: 0 auto;
}

.step {
    display: flex;
    align-items: center;
    gap: 30px;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.step:hover {
    background: #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateX(10px);
}

.step-number {
    width: 60px;
    height: 60px;
    background: #e74c3c;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 700;
    flex-shrink: 0;
}

.step-content h3 {
    color: #333;
    font-size: 1.3rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.step-content p {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

/* Benefits Section */
.benefits-section {
    margin-top: 50px;
    padding-top: 40px;
    border-top: 2px solid #f0f0f0;
}

.benefits-section h2 {
    text-align: center;
    color: #333;
    font-size: 2.2rem;
    margin-bottom: 40px;
    font-weight: 700;
}

.benefits-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
}

.benefit-item {
    text-align: center;
    padding: 25px;
    background: #f8f9fa;
    border-radius: 12px;
    transition: all 0.3s ease;
}

.benefit-item:hover {
    background: #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateY(-3px);
}

.benefit-icon {
    font-size: 2.5rem;
    margin-bottom: 15px;
    display: block;
}

.benefit-item h4 {
    color: #333;
    font-size: 1.2rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.benefit-item p {
    color: #666;
    line-height: 1.5;
    font-size: 0.9rem;
    margin: 0;
}

/* Statistics Section */
.stats-section {
    margin-top: 50px;
    padding: 40px 0;
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    border-radius: 15px;
    color: white;
    text-align: center;
}

.stats-section h2 {
    color: white;
    font-size: 2.2rem;
    margin-bottom: 40px;
    font-weight: 700;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 30px;
    max-width: 800px;
    margin: 0 auto;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 10px;
    color: white;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-label {
    font-size: 1.1rem;
    opacity: 0.9;
    font-weight: 500;
}

/* FAQ Section */
.faq-section {
    margin-top: 50px;
    padding-top: 40px;
    border-top: 2px solid #f0f0f0;
}

.faq-section h2 {
    text-align: center;
    color: #333;
    font-size: 2.2rem;
    margin-bottom: 40px;
    font-weight: 700;
}

.faq-container {
    max-width: 800px;
    margin: 0 auto;
}

.faq-item {
    background: #f8f9fa;
    padding: 25px;
    border-radius: 12px;
    margin-bottom: 20px;
    transition: all 0.3s ease;
    border-left: 4px solid #e74c3c;
}

.faq-item:hover {
    background: #fff;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transform: translateX(5px);
}

.faq-item h4 {
    color: #333;
    font-size: 1.2rem;
    margin-bottom: 15px;
    font-weight: 600;
}

.faq-item p {
    color: #666;
    line-height: 1.6;
    margin: 0;
}

/* Call to Action Section */
.cta-section {
    margin-top: 50px;
    padding: 50px 0;
    background: linear-gradient(135deg, #2c3e50, #34495e);
    border-radius: 15px;
    text-align: center;
    color: white;
}

.cta-content h2 {
    color: white;
    font-size: 2.5rem;
    margin-bottom: 20px;
    font-weight: 700;
}

.cta-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    opacity: 0.9;
}

.cta-button {
    background: #e74c3c;
    color: white;
    border: none;
    padding: 18px 40px;
    font-size: 1.2rem;
    font-weight: 600;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
}

.cta-button:hover {
    background: #c0392b;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.4);
}

/* Footer */
footer {
    text-align: center;
    margin-top: 40px;
    color: white;
    opacity: 0.8;
}

.footer-links {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.footer-links a {
    color: white;
    text-decoration: none;
    padding: 5px 10px;
    border-radius: 5px;
    transition: all 0.3s ease;
    opacity: 0.8;
}

.footer-links a:hover {
    opacity: 1;
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

/* Responsive design */
@media (max-width: 768px) {
    body {
        padding-top: 70px;
        /* Adjust for smaller mobile nav */
    }

    .nav-container {
        height: 60px;
        padding: 0 15px;
    }

    .brand-link {
        font-size: 1.1rem;
    }

    .brand-icon {
        width: 24px;
        height: 24px;
        margin-right: 8px;
    }

    .nav-menu {
        position: absolute;
        top: 100%;
        height: 100vh;
        align-items: start;
        left: 0;
        right: 0;
        background: rgba(44, 62, 80, 0.98);
        backdrop-filter: blur(15px);
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transform: translateY(-100%);
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease;
    }

    .nav-menu.active {
        transform: translateY(0);
        opacity: 1;
        visibility: visible;
    }

    .nav-links {
        width: 100%;
        flex-direction: column;
        padding: 20px;
        gap: 35px;
    }

    .nav-link {
        padding: 12px 20px;
        border-radius: 8px;
        text-align: center;
        background: rgba(255, 255, 255, 0.05);
        border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active .nav-toggle-bar:nth-child(1) {
        transform: rotate(45deg) translate(6px, 6px);
    }

    .nav-toggle.active .nav-toggle-bar:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active .nav-toggle-bar:nth-child(3) {
        transform: rotate(-45deg) translate(6px, -6px);
    }

    .container {
        padding: 10px;
    }

    header h1 {
        font-size: 2rem;
    }

    main {
        padding: 20px;
    }

    .compression-settings {
        flex-direction: column;
        gap: 20px;
    }

    .image-item {
        flex-direction: column;
        text-align: center;
    }

    .image-preview {
        margin-right: 0;
        margin-bottom: 15px;
    }

    .action-buttons {
        flex-direction: column;
    }

    .footer-links {
        flex-direction: column;
        gap: 10px;
    }

    .top-nav {
        top: 10px;
        right: 10px;
        left: auto;
    }

    .top-nav .back-link {
        padding: 6px 12px;
        font-size: 0.8rem;
    }

    /* Mobile styles for new sections */
    .hero-features {
        flex-direction: column;
        gap: 15px;
        align-items: center;
    }

    .feature-badge {
        padding: 10px 16px;
        font-size: 0.9rem;
    }

    .features-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .feature-card {
        padding: 25px 20px;
    }

    .features-section h2,
    .how-it-works-section h2,
    .benefits-section h2 {
        font-size: 1.8rem;
        margin-bottom: 30px;
    }

    .step {
        flex-direction: column;
        text-align: center;
        gap: 20px;
        padding: 20px;
    }

    .step:hover {
        transform: translateY(-3px);
    }

    .step-number {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
    }

    .benefits-grid {
        grid-template-columns: 1fr;
        gap: 20px;
    }

    .benefit-item {
        padding: 20px;
    }

    /* Mobile styles for stats and FAQ */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .stat-number {
        font-size: 2.5rem;
    }

    .stat-label {
        font-size: 1rem;
    }

    .faq-item {
        padding: 20px;
        margin-bottom: 15px;
    }

    .faq-item h4 {
        font-size: 1.1rem;
    }

    /* Mobile styles for CTA */
    .cta-content h2 {
        font-size: 2rem;
    }

    .cta-content p {
        font-size: 1.1rem;
    }

    .cta-button {
        padding: 15px 30px;
        font-size: 1.1rem;
    }
}
}

/* Loading animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #e74c3c;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Page styles for legal pages */
.page-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    padding-top: 100px;
    /* Add space for fixed navigation */
}

.page-header {
    text-align: center;
    margin-bottom: 40px;
    color: white;
}

.page-content {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    line-height: 1.8;
}

.page-content h1 {
    color: #333;
    margin-bottom: 30px;
    font-size: 2.5rem;
}

.page-content h2 {
    color: #e74c3c;
    margin-top: 30px;
    margin-bottom: 15px;
    font-size: 1.5rem;
}

.page-content p {
    margin-bottom: 15px;
    color: #555;
}

.page-content ul {
    margin-bottom: 15px;
    padding-left: 30px;
}

.page-content li {
    margin-bottom: 8px;
    color: #555;
}

.back-link {
    display: inline-block;
    margin-top: 30px;
    padding: 10px 20px;
    background: #e74c3c;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.back-link:hover {
    background: #c0392b;
    transform: translateY(-2px);
}

.top-nav .back-link {
    margin-top: 0;
    padding: 8px 16px;
    font-size: 0.9rem;
    background: rgba(231, 76, 60, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.top-nav .back-link:hover {
    background: rgba(192, 57, 43, 0.95);
}