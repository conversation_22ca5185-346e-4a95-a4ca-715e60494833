<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - trakmaze</title>
    <link rel="stylesheet" href="../css/styles.css">
</head>

<body>
    <!-- Navigation Header -->
    <nav class="top-navigation">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="../index.html" class="brand-link">
                    <svg class="brand-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <circle cx="8.5" cy="8.5" r="1.5"></circle>
                        <polyline points="21,15 16,10 5,21"></polyline>
                    </svg>
                    <span class="brand-text">trakmaze</span>
                </a>
            </div>

            <div class="nav-menu" id="navMenu">
                <ul class="nav-links">
                    <li><a href="../index.html" class="nav-link">Home</a></li>
                    <li><a href="about-us.html" class="nav-link">About Us</a></li>
                    <li><a href="contact-us.html" class="nav-link active">Contact Us</a></li>
                    <li><a href="privacy-policy.html" class="nav-link">Privacy Policy</a></li>
                </ul>
            </div>

            <div class="nav-toggle" id="navToggle">
                <span class="nav-toggle-bar"></span>
                <span class="nav-toggle-bar"></span>
                <span class="nav-toggle-bar"></span>
            </div>
        </div>
    </nav>

    <div class="page-container">
        <header class="page-header">
            <h1>Contact Us</h1>
            <p>Get in touch with the trakmaze team</p>
        </header>

        <main class="page-content">
            <nav class="top-nav">
                <a href="../index.html" class="back-link">← Back to Home</a>
            </nav>
            <h1>Contact Us</h1>

            <h2>Get Support</h2>
            <p>We're here to help you get the most out of trakmaze. Whether you have questions, need technical
                support, or want to provide feedback, we'd love to hear from you.</p>

            <h2>Frequently Asked Questions</h2>
            <p>Before reaching out, you might find the answer to your question in our FAQ section:</p>
            <ul>
                <li><strong>Is my data safe?</strong> Yes, all image processing happens locally in your browser. We
                    never upload or store your images.</li>
                <li><strong>What file formats are supported?</strong> We support JPEG, PNG, WebP, GIF, and BMP input
                    formats.</li>
                <li><strong>What's the maximum file size?</strong> You can compress images up to 10MB each.</li>
                <li><strong>Can I use this tool offline?</strong> Yes, after the initial page load, the tool works
                    completely offline.</li>
                <li><strong>Is there a limit to how many images I can compress?</strong> No, you can compress as many
                    images as you need.</li>
            </ul>

            <h2>Technical Support</h2>
            <p>If you're experiencing technical issues with trakmaze, please try the following steps first:</p>
            <ul>
                <li>Refresh the page and try again</li>
                <li>Clear your browser cache and cookies</li>
                <li>Try using a different browser (Chrome, Firefox, Safari, or Edge)</li>
                <li>Ensure your browser is up to date</li>
                <li>Check that JavaScript is enabled in your browser</li>
            </ul>

            <h2>Browser Compatibility</h2>
            <p>trakmaze works best with modern browsers. We recommend:</p>
            <ul>
                <li>Google Chrome 80 or later</li>
                <li>Mozilla Firefox 75 or later</li>
                <li>Safari 13 or later</li>
                <li>Microsoft Edge 80 or later</li>
            </ul>

            <h2>Feature Requests</h2>
            <p>We're always looking to improve trakmaze. If you have ideas for new features or improvements,
                we'd love to hear them. Some features we're considering include:</p>
            <ul>
                <li>Additional image format support</li>
                <li>Advanced compression algorithms</li>
                <li>Batch processing improvements</li>
                <li>Image editing capabilities</li>
                <li>Custom compression presets</li>
            </ul>

            <h2>Bug Reports</h2>
            <p>If you encounter a bug or unexpected behavior, please provide as much detail as possible:</p>
            <ul>
                <li>What browser and version you're using</li>
                <li>What operating system you're on</li>
                <li>Steps to reproduce the issue</li>
                <li>What you expected to happen</li>
                <li>What actually happened</li>
                <li>Any error messages you received</li>
            </ul>

            <h2>Privacy and Security</h2>
            <p>Your privacy is important to us. trakmaze:</p>
            <ul>
                <li>Processes all images locally in your browser</li>
                <li>Never uploads your images to our servers</li>
                <li>Doesn't collect personal information</li>
                <li>Doesn't use tracking cookies</li>
                <li>Works completely offline after initial load</li>
            </ul>

            <h2>Business Inquiries</h2>
            <p>For business partnerships, licensing inquiries, or other commercial questions, please include relevant
                details about your organization and how you'd like to work with us.</p>

            <h2>Response Time</h2>
            <p>We strive to respond to all inquiries within 24-48 hours during business days. For urgent technical
                issues, please include "URGENT" in your subject line.</p>

            <h2>Community</h2>
            <p>Join our community of users who are passionate about image optimization and web performance. Share tips,
                ask questions, and help others get the most out of trakmaze.</p>

            <h2>Stay Updated</h2>
            <p>Follow us for updates on new features, improvements, and tips for better image compression:</p>
            <ul>
                <li>Check back regularly for new features</li>
                <li>Bookmark this page for easy access</li>
                <li>Share trakmaze with others who might find it useful</li>
            </ul>
            <h2>E-Mail</h2>
            <p><EMAIL> </p>
            <p><strong>Thank you for using trakmaze!</strong></p>

            <a href="../index.html" class="back-link">← Back to trakmaze</a>
        </main>

        <footer>
            <p>&copy; 2025 trakmaze.com All rights reserved. <br>All processing is done locally in your browser.</p>
            <div class="footer-links">
                <a href="about-us.html">About Us</a>
                <a href="contact-us.html">Contact Us</a>
                <a href="terms-of-use.html">Terms of Use</a>
                <a href="privacy-policy.html">Privacy Policy</a>
                <a href="terms-of-service.html">Terms of Service</a>
            </div>
        </footer>
    </div>

    <script>
        // Simple navigation toggle for mobile
        document.addEventListener('DOMContentLoaded', function () {
            const navToggle = document.getElementById('navToggle');
            const navMenu = document.getElementById('navMenu');

            if (navToggle && navMenu) {
                navToggle.addEventListener('click', () => {
                    navToggle.classList.toggle('active');
                    navMenu.classList.toggle('active');
                });

                // Close menu when clicking on a link
                const navLinks = navMenu.querySelectorAll('.nav-link');
                navLinks.forEach(link => {
                    link.addEventListener('click', () => {
                        navToggle.classList.remove('active');
                        navMenu.classList.remove('active');
                    });
                });

                // Close menu when clicking outside
                document.addEventListener('click', (e) => {
                    if (!navToggle.contains(e.target) && !navMenu.contains(e.target)) {
                        navToggle.classList.remove('active');
                        navMenu.classList.remove('active');
                    }
                });
            }
        });
    </script>
</body>

</html>