<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Privacy Policy - trakmaze</title>
    <link rel="stylesheet" href="../css/styles.css">
</head>

<body>
    <!-- Navigation Header -->
    <nav class="top-navigation">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="../index.html" class="brand-link">
                    <svg class="brand-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <circle cx="8.5" cy="8.5" r="1.5"></circle>
                        <polyline points="21,15 16,10 5,21"></polyline>
                    </svg>
                    <span class="brand-text">trakmaze</span>
                </a>
            </div>

            <div class="nav-menu" id="navMenu">
                <ul class="nav-links">
                    <li><a href="../index.html" class="nav-link">Home</a></li>
                    <li><a href="compressor.html" class="nav-link">Image Compressor</a></li>
                    <li><a href="about-us.html" class="nav-link">About Us</a></li>
                    <li><a href="contact-us.html" class="nav-link">Contact Us</a></li>
                    <li><a href="privacy-policy.html" class="nav-link active">Privacy Policy</a></li>
                </ul>
            </div>

            <div class="nav-toggle" id="navToggle">
                <span class="nav-toggle-bar"></span>
                <span class="nav-toggle-bar"></span>
                <span class="nav-toggle-bar"></span>
            </div>
        </div>
    </nav>

    <div class="page-container">
        <header class="page-header">
            <h1>Privacy Policy</h1>
            <p>Your privacy is important to us</p>
        </header>

        <main class="page-content">
            <nav class="top-nav">
                <a href="../index.html" class="back-link">← Back to Home</a>
            </nav>
            <h1>Privacy Policy</h1>
            <p><strong>Last updated:</strong> August 1, 2025</p>

            <h2>1. Information We Collect</h2>
            <p>trakmaze is designed with privacy in mind. We want to be completely transparent about what
                information we collect and how we use it:</p>
            <ul>
                <li><strong>No Personal Data Collection:</strong> We do not collect, store, or transmit any personal
                    information about our users.</li>
                <li><strong>No Image Storage:</strong> All images you upload are processed entirely in your browser. We
                    never receive, store, or have access to your images.</li>
                <li><strong>No Tracking:</strong> We do not use cookies, analytics, or any tracking technologies to
                    monitor your usage.</li>
                <li><strong>No Account Required:</strong> Our service does not require registration or account creation.
                </li>
            </ul>

            <h2>2. How Our Service Works</h2>
            <p>Our image compression tool operates entirely within your web browser using client-side JavaScript:</p>
            <ul>
                <li>When you select or drag images into our tool, they are processed locally on your device</li>
                <li>No data is sent to our servers or any third-party services</li>
                <li>All compression happens using your browser's built-in capabilities</li>
                <li>Compressed images are generated and downloaded directly from your browser</li>
            </ul>

            <h2>3. Data Security</h2>
            <p>Since we don't collect or store any data, there are no security risks associated with data breaches from
                our service:</p>
            <ul>
                <li>Your images never leave your device</li>
                <li>No server-side processing or storage occurs</li>
                <li>All operations are performed locally in your browser</li>
                <li>You maintain complete control over your images at all times</li>
            </ul>

            <h2>4. Third-Party Services</h2>
            <p>Our website may include links to external websites that are not under our control. We cannot
                guarantee the accuracy of their content or the security of their data practices. If you choose to
                visit a third-party site, you do so at your own risk. Please review their privacy policy before
                proceeding.</p>

            <h2>5. Browser Storage</h2>
            <p>Our application may temporarily store data in your browser's local memory during the compression process,
                but this data is automatically cleared when you:</p>
            <ul>
                <li>Close the browser tab</li>
                <li>Refresh the page</li>
                <li>Clear your browser's cache</li>
                <li>Use the "Clear All" function in our tool</li>
            </ul>

            <h2>6. Children's Privacy</h2>
            <p>Our service is safe for users of all ages. Since we don't collect any personal information, there are no
                special considerations for children's privacy under COPPA or similar regulations.</p>

            <h2>7. International Users</h2>
            <p>Since our service operates entirely in your browser without data collection, it complies with
                international privacy regulations including GDPR, CCPA, and other privacy laws.</p>

            <h2>8. Changes to This Policy</h2>
            <p>If we ever change how our service works in a way that affects privacy, we will update this policy and
                clearly indicate the changes. However, our commitment to not collecting user data will remain unchanged.
            </p>

            <h2>9. Contact Information</h2>
            <p>If you have any questions about this privacy policy or our practices, you can contact us through our
                About Us page.</p>

            <h2>10. Your Rights</h2>
            <p>Since we don't collect any personal data, traditional data rights (like access, deletion, or portability)
                don't apply. However, you always have complete control over your images and can:</p>
            <ul>
                <li>Use our service without providing any personal information</li>
                <li>Process images completely offline after the initial page load</li>
                <li>Clear all data by refreshing the page or closing the browser</li>
            </ul>

            <p><strong>Summary:</strong> trakmaze is a privacy-first tool that processes your images entirely in
                your browser. We don't collect, store, or transmit any of your data or images. Your privacy is
                completely protected.</p>

            <a href="../index.html" class="back-link">← Back to trakmaze</a>
        </main>

        <footer>
            <p>&copy; 2025 trakmaze.com All rights reserved. <br>All processing is done locally in your browser.</p>
            <div class="footer-links">
                <a href="about-us.html">About Us</a>
                <a href="contact-us.html">Contact Us</a>
                <a href="terms-of-use.html">Terms of Use</a>
                <a href="privacy-policy.html">Privacy Policy</a>
                <a href="terms-of-service.html">Terms of Service</a>
            </div>
        </footer>
    </div>

    <script>
        // Simple navigation toggle for mobile
        document.addEventListener('DOMContentLoaded', function () {
            const navToggle = document.getElementById('navToggle');
            const navMenu = document.getElementById('navMenu');

            if (navToggle && navMenu) {
                navToggle.addEventListener('click', () => {
                    navToggle.classList.toggle('active');
                    navMenu.classList.toggle('active');
                });

                // Close menu when clicking on a link
                const navLinks = navMenu.querySelectorAll('.nav-link');
                navLinks.forEach(link => {
                    link.addEventListener('click', () => {
                        navToggle.classList.remove('active');
                        navMenu.classList.remove('active');
                    });
                });

                // Close menu when clicking outside
                document.addEventListener('click', (e) => {
                    if (!navToggle.contains(e.target) && !navMenu.contains(e.target)) {
                        navToggle.classList.remove('active');
                        navMenu.classList.remove('active');
                    }
                });
            }
        });
    </script>
</body>

</html>