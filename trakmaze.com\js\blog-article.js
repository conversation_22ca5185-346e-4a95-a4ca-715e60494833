// Blog Article Content Data
const articleContent = {
    'webp-complete-guide': {
        content: `
            <h2>Introduction to WebP</h2>
            <p>WebP is a modern image format developed by Google that provides superior lossless and lossy compression for images on the web. Using WebP, webmasters and web developers can create smaller, richer images that make the web faster.</p>
            
            <h2>Key Benefits of WebP</h2>
            <ul>
                <li><strong>Superior Compression:</strong> WebP lossless images are 26% smaller in size compared to PNGs</li>
                <li><strong>Quality Retention:</strong> WebP lossy images are 25-35% smaller than comparable JPEG images</li>
                <li><strong>Transparency Support:</strong> WebP supports lossless transparency with just 22% additional bytes</li>
                <li><strong>Animation Support:</strong> WebP supports animated images as an alternative to animated GIFs</li>
            </ul>
            
            <h2>Browser Support and Implementation</h2>
            <p>WebP is now supported by all major browsers including Chrome, Firefox, Safari, and Edge. However, it's important to implement proper fallbacks for older browsers.</p>
            
            <blockquote>
                "WebP typically achieves an average of 30% more compression than JPEG and JPEG 2000, without loss of image quality." - Google Developers
            </blockquote>
            
            <h2>Implementation Strategies</h2>
            <h3>Using the Picture Element</h3>
            <pre><code>&lt;picture&gt;
  &lt;source srcset="image.webp" type="image/webp"&gt;
  &lt;source srcset="image.jpg" type="image/jpeg"&gt;
  &lt;img src="image.jpg" alt="Description"&gt;
&lt;/picture&gt;</code></pre>
            
            <h3>Server-Side Detection</h3>
            <p>You can also implement WebP serving through server-side detection of the Accept header, automatically serving WebP to supporting browsers and fallback formats to others.</p>
            
            <h2>Conversion Tools and Techniques</h2>
            <p>There are several ways to convert your existing images to WebP format:</p>
            <ul>
                <li>Command-line tools like cwebp</li>
                <li>Online converters like TrakMaze</li>
                <li>Build tools and plugins for automated conversion</li>
                <li>CDN services with automatic format optimization</li>
            </ul>
            
            <h2>Best Practices</h2>
            <ol>
                <li>Always provide fallbacks for older browsers</li>
                <li>Test quality settings to find the optimal balance</li>
                <li>Consider your audience's browser usage statistics</li>
                <li>Implement progressive enhancement strategies</li>
                <li>Monitor performance improvements after implementation</li>
            </ol>
            
            <h2>Conclusion</h2>
            <p>WebP represents a significant advancement in image compression technology. By implementing WebP in your web projects, you can achieve substantial file size reductions while maintaining excellent image quality, leading to faster loading times and improved user experience.</p>
        `
    },
    'jpeg-compression-explained': {
        content: `
            <h2>Understanding JPEG Compression</h2>
            <p>JPEG (Joint Photographic Experts Group) compression is a lossy compression method specifically designed for photographic images. Understanding how it works helps you make better decisions about quality settings and optimization.</p>
            
            <h2>How JPEG Compression Works</h2>
            <p>JPEG compression works through several steps:</p>
            <ol>
                <li><strong>Color Space Conversion:</strong> RGB is converted to YCbCr color space</li>
                <li><strong>Chroma Subsampling:</strong> Color information is reduced while preserving luminance</li>
                <li><strong>Block Division:</strong> Image is divided into 8x8 pixel blocks</li>
                <li><strong>DCT Transform:</strong> Discrete Cosine Transform is applied to each block</li>
                <li><strong>Quantization:</strong> High-frequency components are reduced based on quality setting</li>
                <li><strong>Entropy Encoding:</strong> Final compression using Huffman coding</li>
            </ol>
            
            <h2>Quality Settings Explained</h2>
            <p>JPEG quality is typically expressed as a percentage from 1-100:</p>
            <ul>
                <li><strong>90-100%:</strong> Excellent quality, minimal compression artifacts</li>
                <li><strong>80-89%:</strong> High quality, good for professional photography</li>
                <li><strong>70-79%:</strong> Good quality, optimal for web use</li>
                <li><strong>60-69%:</strong> Acceptable quality, noticeable compression</li>
                <li><strong>Below 60%:</strong> Poor quality, significant artifacts</li>
            </ul>
            
            <h2>Finding the Perfect Balance</h2>
            <p>The key to effective JPEG compression is finding the sweet spot between file size and visual quality. This depends on several factors:</p>
            
            <h3>Image Content Considerations</h3>
            <ul>
                <li>High-detail images need higher quality settings</li>
                <li>Images with gradients are more sensitive to compression</li>
                <li>Images with sharp edges may show artifacts at low quality</li>
                <li>Skin tones require careful quality balance</li>
            </ul>
            
            <h3>Use Case Optimization</h3>
            <ul>
                <li><strong>Web thumbnails:</strong> 60-70% quality is often sufficient</li>
                <li><strong>Hero images:</strong> 80-85% quality for best impression</li>
                <li><strong>Product photos:</strong> 75-85% quality for detail preservation</li>
                <li><strong>Background images:</strong> 70-75% quality is typically adequate</li>
            </ul>
            
            <h2>Advanced JPEG Techniques</h2>
            <h3>Progressive JPEG</h3>
            <p>Progressive JPEG encoding allows images to load in multiple passes, improving perceived performance by showing a low-quality version first that gradually improves.</p>
            
            <h3>Chroma Subsampling</h3>
            <p>This technique reduces color information while preserving brightness, taking advantage of human visual perception to achieve better compression.</p>
            
            <h2>Common Mistakes to Avoid</h2>
            <ul>
                <li>Using JPEG for images with transparency</li>
                <li>Repeatedly saving JPEG files (generation loss)</li>
                <li>Using very low quality for important images</li>
                <li>Not considering the viewing context</li>
                <li>Ignoring file size requirements</li>
            </ul>
            
            <h2>Tools for JPEG Optimization</h2>
            <p>Several tools can help you optimize JPEG compression:</p>
            <ul>
                <li>TrakMaze online compressor with real-time preview</li>
                <li>Adobe Photoshop's "Save for Web" feature</li>
                <li>Command-line tools like jpegoptim</li>
                <li>Automated build tools and plugins</li>
            </ul>
        `
    }
    // Add more article content as needed
};

// Initialize article page
document.addEventListener('DOMContentLoaded', () => {
    loadArticle();
    setupShareButtons();
    setupRelatedArticles();
});

function loadArticle() {
    const urlParams = new URLSearchParams(window.location.search);
    const articleId = urlParams.get('id');
    
    if (!articleId) {
        showArticleNotFound();
        return;
    }
    
    // Find article in blog data
    const article = window.blogArticles ? 
        window.blogArticles.find(a => a.id === articleId) : 
        null;
    
    if (!article) {
        showArticleNotFound();
        return;
    }
    
    // Update page title and meta
    document.getElementById('articleTitle').textContent = article.title + ' - TrakMaze Blog';
    document.getElementById('breadcrumbTitle').textContent = article.title;
    document.getElementById('articleCategory').textContent = getCategoryName(article.category);
    document.getElementById('articleHeading').textContent = article.title;
    document.getElementById('articleAuthor').textContent = article.author;
    document.getElementById('articleDate').textContent = formatDate(article.date);
    document.getElementById('articleReadTime').textContent = article.readTime;
    document.getElementById('articleUpdated').textContent = formatDate(article.updated);
    
    // Set article image
    const articleImage = document.getElementById('articleImage');
    if (article.image) {
        articleImage.src = article.image;
        articleImage.alt = article.title;
    }
    
    // Load article content
    const articleBody = document.getElementById('articleBody');
    const content = articleContent[articleId];
    
    if (content) {
        articleBody.innerHTML = content.content;
    } else {
        // Generate placeholder content
        articleBody.innerHTML = generatePlaceholderContent(article);
    }
    
    // Display tags
    displayTags(article.tags);
}

function showArticleNotFound() {
    document.getElementById('articleHeading').textContent = 'Article Not Found';
    document.getElementById('articleBody').innerHTML = `
        <div style="text-align: center; padding: 60px 0;">
            <h2>Sorry, this article could not be found.</h2>
            <p>The article you're looking for might have been moved or doesn't exist.</p>
            <a href="blog.html" class="read-more-btn">Back to Blog</a>
        </div>
    `;
}

function generatePlaceholderContent(article) {
    return `
        <h2>Introduction</h2>
        <p>${article.excerpt}</p>
        
        <h2>Key Points</h2>
        <p>This article covers important aspects of ${article.title.toLowerCase()}. The content provides valuable insights for developers, designers, and anyone interested in image optimization and web performance.</p>
        
        <ul>
            <li>Comprehensive overview of the topic</li>
            <li>Practical implementation strategies</li>
            <li>Best practices and recommendations</li>
            <li>Real-world examples and use cases</li>
        </ul>
        
        <h2>Implementation Guide</h2>
        <p>Follow these steps to implement the techniques discussed in this article:</p>
        
        <ol>
            <li>Assess your current setup and requirements</li>
            <li>Choose the appropriate tools and methods</li>
            <li>Implement the optimization techniques</li>
            <li>Test and measure the results</li>
            <li>Iterate and improve based on feedback</li>
        </ol>
        
        <blockquote>
            "Proper image optimization is crucial for modern web performance and user experience."
        </blockquote>
        
        <h2>Best Practices</h2>
        <p>When implementing these techniques, keep these best practices in mind:</p>
        
        <ul>
            <li>Always test your optimizations thoroughly</li>
            <li>Consider your target audience and their devices</li>
            <li>Monitor performance metrics regularly</li>
            <li>Stay updated with the latest developments</li>
            <li>Balance quality with file size requirements</li>
        </ul>
        
        <h2>Conclusion</h2>
        <p>Understanding and implementing proper image optimization techniques is essential for creating fast, efficient websites. By following the guidelines and best practices outlined in this article, you can significantly improve your site's performance and user experience.</p>
        
        <p>For more advanced optimization techniques, try our <a href="compressor.html">TrakMaze Image Compressor</a> tool, which provides professional-grade compression with real-time preview and batch processing capabilities.</p>
    `;
}

function displayTags(tags) {
    const tagsContainer = document.getElementById('articleTags');
    if (!tags || tags.length === 0) {
        tagsContainer.style.display = 'none';
        return;
    }
    
    tagsContainer.innerHTML = '<h3>Tags:</h3>' + 
        tags.map(tag => `<span class="tag">${tag}</span>`).join('');
}

function setupShareButtons() {
    // Share buttons are set up with onclick handlers in HTML
}

function shareOnTwitter() {
    const title = document.getElementById('articleHeading').textContent;
    const url = window.location.href;
    const text = `Check out this article: ${title}`;
    const twitterUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`;
    window.open(twitterUrl, '_blank');
}

function shareOnFacebook() {
    const url = window.location.href;
    const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(url)}`;
    window.open(facebookUrl, '_blank');
}

function shareOnLinkedIn() {
    const title = document.getElementById('articleHeading').textContent;
    const url = window.location.href;
    const linkedinUrl = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`;
    window.open(linkedinUrl, '_blank');
}

function copyLink() {
    navigator.clipboard.writeText(window.location.href).then(() => {
        // Show temporary notification
        const btn = event.target.closest('.share-btn');
        const originalText = btn.innerHTML;
        btn.innerHTML = '<span class="share-icon">✓</span> Copied!';
        setTimeout(() => {
            btn.innerHTML = originalText;
        }, 2000);
    });
}

function setupRelatedArticles() {
    const urlParams = new URLSearchParams(window.location.search);
    const currentArticleId = urlParams.get('id');
    
    if (!window.blogArticles) return;
    
    const currentArticle = window.blogArticles.find(a => a.id === currentArticleId);
    if (!currentArticle) return;
    
    // Find related articles (same category, excluding current)
    const relatedArticles = window.blogArticles
        .filter(a => a.category === currentArticle.category && a.id !== currentArticleId)
        .slice(0, 3);
    
    const relatedContainer = document.getElementById('relatedArticles');
    if (relatedArticles.length === 0) {
        relatedContainer.innerHTML = '<p>No related articles found.</p>';
        return;
    }
    
    relatedContainer.innerHTML = relatedArticles.map(article => `
        <div class="article-card">
            <div class="article-image">
                <img src="${article.image}" alt="${article.title}" onerror="this.style.display='none'">
                <div class="article-category">${getCategoryName(article.category)}</div>
            </div>
            <div class="article-content">
                <h3><a href="blog-article.html?id=${article.id}">${article.title}</a></h3>
                <p class="article-excerpt">${article.excerpt}</p>
                <div class="article-meta">
                    <span class="author">By ${article.author}</span>
                    <span class="date">${formatDate(article.date)}</span>
                    <span class="read-time">${article.readTime}</span>
                </div>
                <a href="blog-article.html?id=${article.id}" class="read-more-btn">Read More</a>
            </div>
        </div>
    `).join('');
}

function getCategoryName(category) {
    const categoryNames = {
        'web-optimization': 'Web Optimization',
        'compression-techniques': 'Compression Techniques',
        'tutorials': 'Tutorials',
        'industry-insights': 'Industry Insights',
        'tools-tips': 'Tools & Tips'
    };
    return categoryNames[category] || category;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    });
}
