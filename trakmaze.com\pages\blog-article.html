<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title id="articleTitle">Blog Article - TrakMaze</title>
    <link rel="stylesheet" href="../css/styles.css?v=0.0.2">
    <link rel="stylesheet" href="../css/blog.css?v=0.0.1">
</head>

<body>
    <!-- Navigation Header -->
    <nav class="top-navigation">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="../index.html" class="brand-link">
                    <svg class="brand-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <circle cx="8.5" cy="8.5" r="1.5"></circle>
                        <polyline points="21,15 16,10 5,21"></polyline>
                    </svg>
                    <span class="brand-text">trakmaze</span>
                </a>
            </div>

            <div class="nav-menu" id="navMenu">
                <ul class="nav-links">
                    <li><a href="../index.html" class="nav-link">Home</a></li>
                    <li><a href="compressor.html" class="nav-link">Image Compressor</a></li>
                    <li><a href="blog.html" class="nav-link active">Blog</a></li>
                    <li><a href="about-us.html" class="nav-link">About Us</a></li>
                    <li><a href="contact-us.html" class="nav-link">Contact Us</a></li>
                    <li><a href="privacy-policy.html" class="nav-link">Privacy Policy</a></li>
                </ul>
            </div>

            <div class="nav-toggle" id="navToggle">
                <span class="nav-toggle-bar"></span>
                <span class="nav-toggle-bar"></span>
                <span class="nav-toggle-bar"></span>
            </div>
        </div>
    </nav>

    <div class="container">
        <!-- Breadcrumb -->
        <nav class="breadcrumb">
            <a href="../index.html">Home</a>
            <span class="separator">›</span>
            <a href="blog.html">Blog</a>
            <span class="separator">›</span>
            <span id="breadcrumbTitle">Article</span>
        </nav>

        <main class="article-main">
            <!-- Article Header -->
            <header class="article-header">
                <div class="article-category-badge" id="articleCategory">Web Optimization</div>
                <h1 id="articleHeading">Loading Article...</h1>
                <div class="article-meta">
                    <div class="meta-item">
                        <span class="meta-label">Author:</span>
                        <span id="articleAuthor">TrakMaze Team</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">Published:</span>
                        <span id="articleDate">Loading...</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">Read Time:</span>
                        <span id="articleReadTime">Loading...</span>
                    </div>
                    <div class="meta-item">
                        <span class="meta-label">Updated:</span>
                        <span id="articleUpdated">Loading...</span>
                    </div>
                </div>
            </header>

            <!-- Article Content -->
            <article class="article-content">
                <div class="article-body" id="articleBody">
                    <!-- Content will be loaded by JavaScript -->
                    <div class="loading-content">
                        <div class="loading-spinner"></div>
                        <p>Loading article content...</p>
                    </div>
                </div>

                <!-- Article Tags -->
                <div class="article-tags" id="articleTags">
                    <!-- Tags will be populated by JavaScript -->
                </div>

            </article>

            <!-- Related Articles -->
            <section class="related-articles">
                <h2>Related Articles</h2>
                <div class="related-grid" id="relatedArticles">
                    <!-- Related articles will be populated by JavaScript -->
                </div>
            </section>



            <!-- Back to Blog -->
            <div class="back-to-blog">
                <a href="blog.html" class="back-btn">
                    <span class="back-icon">←</span>
                    Back to Blog
                </a>
            </div>
        </main>

        <footer>
            <p>&copy; 2025 trakmaze.com All rights reserved. <br>All processing is done locally in your browser.</p>
            <div class="footer-links">
                <a href="../index.html">Home</a>
                <a href="compressor.html">Image Compressor</a>
                <a href="blog.html">Blog</a>
                <a href="about-us.html">About Us</a>
                <a href="contact-us.html">Contact Us</a>
                <a href="terms-of-use.html">Terms of Use</a>
                <a href="privacy-policy.html">Privacy Policy</a>
                <a href="terms-of-service.html">Terms of Service</a>
            </div>
        </footer>
    </div>

    <script src="../js/script.js"></script>
    <script src="../js/blog-article.js"></script>
    <script src="../js/blog-articles-content.js"></script>
    <script src="../js/blog-articles-remaining.js"></script>
    <script src="../js/blog-articles-final.js"></script>
</body>

</html>
