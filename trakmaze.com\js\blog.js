// Blog Articles Data
const blogArticles = [
    {
        id: 'webp-complete-guide',
        title: 'The Complete Guide to WebP: Next-Generation Image Format',
        excerpt: 'Discover how WebP can reduce your image file sizes by up to 35% compared to JPEG while maintaining superior quality. Learn implementation strategies, browser support, and conversion techniques.',
        category: 'web-optimization',
        author: 'TrakMaze Team',
        date: '2024-12-15',
        updated: '2024-12-15',
        readTime: '8 min read',
        tags: ['WebP', 'Image Optimization', 'Web Performance', 'File Formats'],
        featured: true,
        image: '../images/blog/webp-guide.jpg'
    },
    {
        id: 'jpeg-compression-explained',
        title: 'JPEG Compression Explained: Quality vs File Size Balance',
        excerpt: 'Understanding how JPEG compression works and finding the perfect balance between image quality and file size for your specific needs.',
        category: 'compression-techniques',
        author: '<PERSON>',
        date: '2024-12-10',
        updated: '2024-12-12',
        readTime: '6 min read',
        tags: ['JPEG', 'Compression', 'Image Quality', 'File Size'],
        featured: false,
        image: '../images/blog/jpeg-compression.jpg'
    },
    {
        id: 'web-performance-images',
        title: 'How Image Optimization Impacts Web Performance',
        excerpt: 'Learn how properly optimized images can improve your website loading speed, SEO rankings, and user experience.',
        category: 'web-optimization',
        author: 'Mike Rodriguez',
        date: '2024-12-08',
        updated: '2024-12-08',
        readTime: '7 min read',
        tags: ['Web Performance', 'SEO', 'User Experience', 'Loading Speed'],
        featured: false,
        image: '../images/blog/web-performance.jpg'
    },
    {
        id: 'png-optimization-techniques',
        title: 'Advanced PNG Optimization Techniques for Developers',
        excerpt: 'Explore advanced methods to optimize PNG images without losing quality, including palette optimization and transparency handling.',
        category: 'compression-techniques',
        author: 'Alex Thompson',
        date: '2024-12-05',
        updated: '2024-12-05',
        readTime: '9 min read',
        tags: ['PNG', 'Optimization', 'Transparency', 'Lossless Compression'],
        featured: false,
        image: '../images/blog/png-optimization.jpg'
    },
    {
        id: 'responsive-images-guide',
        title: 'Responsive Images: A Complete Implementation Guide',
        excerpt: 'Master responsive images with srcset, sizes, and picture elements to deliver the perfect image for every device and screen size.',
        category: 'tutorials',
        author: 'Emma Wilson',
        date: '2024-12-03',
        updated: '2024-12-03',
        readTime: '10 min read',
        tags: ['Responsive Design', 'HTML', 'CSS', 'Mobile Optimization'],
        featured: false,
        image: '../images/blog/responsive-images.jpg'
    },
    {
        id: 'image-formats-comparison',
        title: 'Image Formats Comparison: JPEG vs PNG vs WebP vs AVIF',
        excerpt: 'Comprehensive comparison of modern image formats, their strengths, weaknesses, and best use cases for different scenarios.',
        category: 'industry-insights',
        author: 'David Park',
        date: '2024-12-01',
        updated: '2024-12-01',
        readTime: '12 min read',
        tags: ['Image Formats', 'Comparison', 'AVIF', 'Format Selection'],
        featured: false,
        image: '../images/blog/formats-comparison.jpg'
    },
    {
        id: 'batch-image-processing',
        title: 'Batch Image Processing: Automating Your Workflow',
        excerpt: 'Learn how to efficiently process hundreds of images at once using batch processing techniques and automation tools.',
        category: 'tutorials',
        author: 'Lisa Chang',
        date: '2024-11-28',
        updated: '2024-11-28',
        readTime: '8 min read',
        tags: ['Batch Processing', 'Automation', 'Workflow', 'Productivity'],
        featured: false,
        image: '../images/blog/batch-processing.jpg'
    },
    {
        id: 'mobile-image-optimization',
        title: 'Mobile Image Optimization: Best Practices for 2024',
        excerpt: 'Optimize images for mobile devices with techniques for reducing data usage while maintaining visual quality on smaller screens.',
        category: 'web-optimization',
        author: 'James Miller',
        date: '2024-11-25',
        updated: '2024-11-25',
        readTime: '7 min read',
        tags: ['Mobile Optimization', 'Data Usage', 'Performance', 'User Experience'],
        featured: false,
        image: '../images/blog/mobile-optimization.jpg'
    },
    {
        id: 'image-compression-algorithms',
        title: 'Understanding Image Compression Algorithms',
        excerpt: 'Deep dive into the mathematics and computer science behind image compression algorithms and how they achieve file size reduction.',
        category: 'compression-techniques',
        author: 'Dr. Robert Kim',
        date: '2024-11-22',
        updated: '2024-11-22',
        readTime: '15 min read',
        tags: ['Algorithms', 'Computer Science', 'Mathematics', 'Technical'],
        featured: false,
        image: '../images/blog/compression-algorithms.jpg'
    },
    {
        id: 'cdn-image-optimization',
        title: 'CDN and Image Optimization: A Perfect Match',
        excerpt: 'Learn how Content Delivery Networks can enhance your image optimization strategy and improve global loading speeds.',
        category: 'web-optimization',
        author: 'Maria Garcia',
        date: '2024-11-20',
        updated: '2024-11-20',
        readTime: '6 min read',
        tags: ['CDN', 'Global Performance', 'Caching', 'Distribution'],
        featured: false,
        image: '../images/blog/cdn-optimization.jpg'
    },
    {
        id: 'image-seo-optimization',
        title: 'Image SEO: Optimizing Images for Search Engines',
        excerpt: 'Complete guide to image SEO including alt text, file names, structured data, and technical optimization for better rankings.',
        category: 'web-optimization',
        author: 'Kevin Brown',
        date: '2024-11-18',
        updated: '2024-11-18',
        readTime: '9 min read',
        tags: ['SEO', 'Search Engines', 'Alt Text', 'Structured Data'],
        featured: false,
        image: '../images/blog/image-seo.jpg'
    },
    {
        id: 'progressive-jpeg-benefits',
        title: 'Progressive JPEG: Improving Perceived Performance',
        excerpt: 'Understand how progressive JPEG encoding can improve user experience by showing images gradually as they load.',
        category: 'compression-techniques',
        author: 'Anna Lee',
        date: '2024-11-15',
        updated: '2024-11-15',
        readTime: '5 min read',
        tags: ['Progressive JPEG', 'User Experience', 'Loading', 'Perception'],
        featured: false,
        image: '../images/blog/progressive-jpeg.jpg'
    },
    {
        id: 'image-accessibility',
        title: 'Image Accessibility: Making Visual Content Inclusive',
        excerpt: 'Learn how to make your images accessible to all users, including those using screen readers and assistive technologies.',
        category: 'tutorials',
        author: 'Rachel Green',
        date: '2024-11-12',
        updated: '2024-11-12',
        readTime: '8 min read',
        tags: ['Accessibility', 'Inclusive Design', 'Screen Readers', 'Alt Text'],
        featured: false,
        image: '../images/blog/image-accessibility.jpg'
    },
    {
        id: 'future-image-formats',
        title: 'The Future of Image Formats: AVIF, HEIF, and Beyond',
        excerpt: 'Explore emerging image formats and technologies that will shape the future of web graphics and digital media.',
        category: 'industry-insights',
        author: 'Tom Wilson',
        date: '2024-11-10',
        updated: '2024-11-10',
        readTime: '11 min read',
        tags: ['Future Technology', 'AVIF', 'HEIF', 'Innovation'],
        featured: false,
        image: '../images/blog/future-formats.jpg'
    },
    {
        id: 'image-compression-tools',
        title: 'Top Image Compression Tools and Software in 2024',
        excerpt: 'Comprehensive review of the best image compression tools available, from online services to desktop applications.',
        category: 'tools-tips',
        author: 'Chris Davis',
        date: '2024-11-08',
        updated: '2024-11-08',
        readTime: '10 min read',
        tags: ['Tools', 'Software', 'Reviews', 'Comparison'],
        featured: false,
        image: '../images/blog/compression-tools.jpg'
    },
    {
        id: 'image-optimization-workflow',
        title: 'Building an Efficient Image Optimization Workflow',
        excerpt: 'Step-by-step guide to creating an automated image optimization workflow for your development and design process.',
        category: 'tutorials',
        author: 'Jennifer Adams',
        date: '2024-11-05',
        updated: '2024-11-05',
        readTime: '12 min read',
        tags: ['Workflow', 'Automation', 'Process', 'Efficiency'],
        featured: false,
        image: '../images/blog/optimization-workflow.jpg'
    },
    {
        id: 'image-quality-metrics',
        title: 'Understanding Image Quality Metrics and Measurements',
        excerpt: 'Learn about different image quality metrics like PSNR, SSIM, and perceptual quality measures used in compression.',
        category: 'compression-techniques',
        author: 'Dr. Susan Lee',
        date: '2024-11-03',
        updated: '2024-11-03',
        readTime: '9 min read',
        tags: ['Quality Metrics', 'PSNR', 'SSIM', 'Measurement'],
        featured: false,
        image: '../images/blog/quality-metrics.jpg'
    },
    {
        id: 'e-commerce-image-optimization',
        title: 'E-commerce Image Optimization: Boosting Sales Through Speed',
        excerpt: 'How proper image optimization can increase conversion rates and sales in e-commerce by improving page load times.',
        category: 'industry-insights',
        author: 'Mark Johnson',
        date: '2024-11-01',
        updated: '2024-11-01',
        readTime: '8 min read',
        tags: ['E-commerce', 'Conversion Rate', 'Sales', 'Performance'],
        featured: false,
        image: '../images/blog/ecommerce-optimization.jpg'
    },
    {
        id: 'image-compression-myths',
        title: 'Common Image Compression Myths Debunked',
        excerpt: 'Separating fact from fiction in image compression, addressing common misconceptions and outdated practices.',
        category: 'industry-insights',
        author: 'Laura Martinez',
        date: '2024-10-29',
        updated: '2024-10-29',
        readTime: '6 min read',
        tags: ['Myths', 'Facts', 'Misconceptions', 'Education'],
        featured: false,
        image: '../images/blog/compression-myths.jpg'
    },
    {
        id: 'advanced-optimization-tips',
        title: '20 Advanced Image Optimization Tips for Professionals',
        excerpt: 'Professional-level tips and tricks for advanced image optimization that go beyond basic compression techniques.',
        category: 'tools-tips',
        author: 'Expert Team',
        date: '2024-10-26',
        updated: '2024-10-26',
        readTime: '14 min read',
        tags: ['Advanced Tips', 'Professional', 'Techniques', 'Expert'],
        featured: false,
        image: '../images/blog/advanced-tips.jpg'
    }
];

// Blog functionality
let currentCategory = 'all';
let displayedArticles = 6;
const articlesPerLoad = 6;

// Initialize blog
document.addEventListener('DOMContentLoaded', () => {
    initializeBlog();
    setupCategoryFilters();
    setupLoadMore();
    setupNewsletter();
});

function initializeBlog() {
    displayArticles();
}

function displayArticles() {
    const articlesGrid = document.getElementById('articlesGrid');
    if (!articlesGrid) return;

    const filteredArticles = currentCategory === 'all' 
        ? blogArticles.filter(article => !article.featured)
        : blogArticles.filter(article => article.category === currentCategory && !article.featured);

    const articlesToShow = filteredArticles.slice(0, displayedArticles);
    
    articlesGrid.innerHTML = '';
    
    articlesToShow.forEach(article => {
        const articleCard = createArticleCard(article);
        articlesGrid.appendChild(articleCard);
    });

    // Update load more button
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
        if (articlesToShow.length >= filteredArticles.length) {
            loadMoreBtn.style.display = 'none';
        } else {
            loadMoreBtn.style.display = 'block';
        }
    }
}

function createArticleCard(article) {
    const card = document.createElement('div');
    card.className = 'article-card';
    card.innerHTML = `
        <div class="article-image">
            <img src="${article.image}" alt="${article.title}" onerror="this.style.display='none'">
            <div class="article-category">${getCategoryName(article.category)}</div>
        </div>
        <div class="article-content">
            <h3><a href="blog-article.html?id=${article.id}">${article.title}</a></h3>
            <p class="article-excerpt">${article.excerpt}</p>
            <div class="article-meta">
                <span class="author">By ${article.author}</span>
                <span class="date">${formatDate(article.date)}</span>
                <span class="read-time">${article.readTime}</span>
            </div>
            <a href="blog-article.html?id=${article.id}" class="read-more-btn">Read More</a>
        </div>
    `;
    return card;
}

function getCategoryName(category) {
    const categoryNames = {
        'web-optimization': 'Web Optimization',
        'compression-techniques': 'Compression Techniques',
        'tutorials': 'Tutorials',
        'industry-insights': 'Industry Insights',
        'tools-tips': 'Tools & Tips'
    };
    return categoryNames[category] || category;
}

function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    });
}

function setupCategoryFilters() {
    const categoryBtns = document.querySelectorAll('.category-btn');
    categoryBtns.forEach(btn => {
        btn.addEventListener('click', () => {
            // Update active state
            categoryBtns.forEach(b => b.classList.remove('active'));
            btn.classList.add('active');
            
            // Update current category
            currentCategory = btn.dataset.category;
            displayedArticles = articlesPerLoad;
            
            // Display filtered articles
            displayArticles();
        });
    });
}

function setupLoadMore() {
    const loadMoreBtn = document.getElementById('loadMoreBtn');
    if (loadMoreBtn) {
        loadMoreBtn.addEventListener('click', () => {
            displayedArticles += articlesPerLoad;
            displayArticles();
        });
    }
}

function setupNewsletter() {
    const newsletterForm = document.querySelector('.newsletter-form');
    if (newsletterForm) {
        newsletterForm.addEventListener('submit', (e) => {
            e.preventDefault();
            const email = e.target.querySelector('input[type="email"]').value;
            
            // Simulate newsletter signup
            alert(`Thank you for subscribing with email: ${email}`);
            e.target.reset();
        });
    }
}

// Export articles data for use in other files
window.blogArticles = blogArticles;
