<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>About Us - trakmaze</title>
    <link rel="stylesheet" href="../css/styles.css">
</head>

<body>
    <!-- Navigation Header -->
    <nav class="top-navigation">
        <div class="nav-container">
            <div class="nav-brand">
                <a href="../index.html" class="brand-link">
                    <svg class="brand-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <circle cx="8.5" cy="8.5" r="1.5"></circle>
                        <polyline points="21,15 16,10 5,21"></polyline>
                    </svg>
                    <span class="brand-text">trakmaze</span>
                </a>
            </div>

            <div class="nav-menu" id="navMenu">
                <ul class="nav-links">
                    <li><a href="../index.html" class="nav-link">Home</a></li>
                    <li><a href="compressor.html" class="nav-link">Image Compressor</a></li>
                    <li><a href="blog.html" class="nav-link">Blog</a></li>
                    <li><a href="about-us.html" class="nav-link active">About Us</a></li>
                    <li><a href="contact-us.html" class="nav-link">Contact Us</a></li>
                    <li><a href="privacy-policy.html" class="nav-link">Privacy Policy</a></li>
                </ul>
            </div>

            <div class="nav-toggle" id="navToggle">
                <span class="nav-toggle-bar"></span>
                <span class="nav-toggle-bar"></span>
                <span class="nav-toggle-bar"></span>
            </div>
        </div>
    </nav>

    <div class="page-container">
        <header class="page-header">
            <h1>About Us</h1>
            <p>Learn more about trakmaze</p>
        </header>

        <main class="page-content">
            <nav class="top-nav">
                <a href="../index.html" class="back-link">← Back to Home</a>
            </nav>
            <h1>About trakmaze</h1>

            <h2>Our Mission</h2>
            <p>trakmaze was created with a simple mission: to provide a fast, secure, and user-friendly way to
                compress images without compromising your privacy. We believe that image optimization should be
                accessible to everyone, regardless of technical expertise.</p>

            <h2>Why We Built This Tool</h2>
            <p>In today's digital world, images are everywhere - from websites and social media to professional
                presentations and personal projects. However, large image files can slow down websites, consume storage
                space, and make sharing difficult. We recognized the need for a solution that:</p>
            <ul>
                <li>Works entirely in your browser for maximum privacy</li>
                <li>Requires no software installation or account creation</li>
                <li>Provides professional-quality compression results</li>
                <li>Supports multiple image formats and batch processing</li>
                <li>Is completely free to use</li>
            </ul>

            <h2>Our Technology</h2>
            <p>trakmaze leverages modern web technologies to deliver powerful image processing capabilities:</p>
            <ul>
                <li><strong>HTML5 Canvas API:</strong> For advanced image manipulation and compression</li>
                <li><strong>Client-Side Processing:</strong> All operations happen in your browser for security</li>
                <li><strong>Modern JavaScript:</strong> Efficient algorithms for optimal performance</li>
                <li><strong>Responsive Design:</strong> Works seamlessly on desktop and mobile devices</li>
                <li><strong>Progressive Web App Features:</strong> Fast loading and offline capabilities</li>
            </ul>

            <h2>Privacy-First Approach</h2>
            <p>Your privacy is our top priority. Unlike many online tools, trakmaze:</p>
            <ul>
                <li>Never uploads your images to our servers</li>
                <li>Processes everything locally in your browser</li>
                <li>Doesn't collect any personal information</li>
                <li>Doesn't use tracking cookies or analytics</li>
                <li>Works completely offline after the initial page load</li>
            </ul>

            <h2>Key Features</h2>
            <ul>
                <li><strong>Multiple Format Support:</strong> JPEG, PNG, WebP, GIF, and BMP input formats</li>
                <li><strong>Format Conversion:</strong> Convert between JPEG, PNG, and WebP</li>
                <li><strong>Adjustable Quality:</strong> Fine-tune compression from 10% to 100%</li>
                <li><strong>Batch Processing:</strong> Compress multiple images simultaneously</li>
                <li><strong>Drag & Drop Interface:</strong> Easy file selection and management</li>
                <li><strong>Real-Time Preview:</strong> See compression results before downloading</li>
                <li><strong>Individual Downloads:</strong> Download images one by one or all at once</li>
                <li><strong>Mobile Friendly:</strong> Fully responsive design for all devices</li>
            </ul>

            <h2>Technical Specifications</h2>
            <ul>
                <li><strong>Maximum File Size:</strong> 10MB per image</li>
                <li><strong>Supported Browsers:</strong> Chrome 80+, Firefox 75+, Safari 13+, Edge 80+</li>
                <li><strong>Processing Speed:</strong> Depends on image size and device capabilities</li>
                <li><strong>Output Quality:</strong> Maintains excellent visual quality while reducing file size</li>
            </ul>

            <h2>Open Source Commitment</h2>
            <p>We believe in transparency and community contribution. trakmaze is built using open web standards
                and follows best practices for:</p>
            <ul>
                <li>Accessibility and usability</li>
                <li>Performance optimization</li>
                <li>Security and privacy</li>
                <li>Cross-browser compatibility</li>
            </ul>

            <h2>Future Development</h2>
            <p>We're continuously working to improve trakmaze. Planned features include:</p>
            <ul>
                <li>Additional image formats support</li>
                <li>Advanced compression algorithms</li>
                <li>Image editing capabilities</li>
                <li>Bulk processing improvements</li>
                <li>Enhanced mobile experience</li>
            </ul>

            <h2>Support and Feedback</h2>
            <p>We value user feedback and are committed to providing the best possible experience. If you encounter any
                issues or have suggestions for improvement, please don't hesitate to reach out.</p>

            <h2>Environmental Impact</h2>
            <p>By compressing images efficiently, our tool helps reduce bandwidth usage and storage requirements,
                contributing to a more sustainable digital environment. Smaller image files mean:</p>
            <ul>
                <li>Faster website loading times</li>
                <li>Reduced server storage costs</li>
                <li>Lower bandwidth consumption</li>
                <li>Better user experience on slower connections</li>
            </ul>

            <h2>Acknowledgments</h2>
            <p>trakmaze is built using modern web technologies and follows industry best practices. We're
                grateful to the open-source community and web standards organizations that make tools like this
                possible.</p>

            <p><strong>Thank you for choosing trakmaze for your image optimization needs!</strong></p>

            <a href="../index.html" class="back-link">← Back to trakmaze</a>
        </main>

        <footer>
            <p>&copy; 2025 trakmaze.com All rights reserved. <br>All processing is done locally in your browser.</p>
            <div class="footer-links">
                <a href="about-us.html">About Us</a>
                <a href="contact-us.html">Contact Us</a>
                <a href="terms-of-use.html">Terms of Use</a>
                <a href="privacy-policy.html">Privacy Policy</a>
                <a href="terms-of-service.html">Terms of Service</a>
            </div>
        </footer>
    </div>

    <script>
        // Simple navigation toggle for mobile
        document.addEventListener('DOMContentLoaded', function () {
            const navToggle = document.getElementById('navToggle');
            const navMenu = document.getElementById('navMenu');

            if (navToggle && navMenu) {
                navToggle.addEventListener('click', () => {
                    navToggle.classList.toggle('active');
                    navMenu.classList.toggle('active');
                });

                // Close menu when clicking on a link
                const navLinks = navMenu.querySelectorAll('.nav-link');
                navLinks.forEach(link => {
                    link.addEventListener('click', () => {
                        navToggle.classList.remove('active');
                        navMenu.classList.remove('active');
                    });
                });

                // Close menu when clicking outside
                document.addEventListener('click', (e) => {
                    if (!navToggle.contains(e.target) && !navMenu.contains(e.target)) {
                        navToggle.classList.remove('active');
                        navMenu.classList.remove('active');
                    }
                });
            }
        });
    </script>
</body>

</html>